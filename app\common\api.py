from flask import Blueprint, request, jsonify, redirect, url_for, session
from app.config import get_db_connection
from werkzeug.security import generate_password_hash, check_password_hash
from app.services.audit_log import AuditLogService
import pymysql
import datetime
import re
import json
import random
import string
from app.services.email_service import send_notification_email

common_bp = Blueprint('common', __name__)

# 联系人保密服务类
class ContactPrivacyService:
    @staticmethod
    def is_contact_confidential(cursor, exhibition_id, contact_type, contact_id):
        """检查联系人是否在指定书展中保密"""
        cursor.execute("""
            SELECT is_confidential
            FROM exhibition_contact_privacy
            WHERE exhibition_id = %s AND contact_type = %s AND contact_id = %s
        """, (exhibition_id, contact_type, contact_id))

        result = cursor.fetchone()
        return result['is_confidential'] if result else False

    @staticmethod
    def check_confidential_permission(cursor, user_id, user_role, exhibition_id):
        """检查用户是否有权查看保密联系人信息"""
        if not user_id:
            return False

        # 管理员可以查看所有信息
        if user_role == 'admin':
            return True

        # 查询书展信息
        cursor.execute("SELECT initiator_id, co_organizer_type, co_organizer_id FROM book_exhibitions WHERE id = %s", (exhibition_id,))
        exhibition = cursor.fetchone()

        if not exhibition:
            return False

        # 书展发起人可以查看
        if user_id == exhibition['initiator_id']:
            return True

        # 协办方用户可以查看
        if exhibition['co_organizer_type'] and exhibition['co_organizer_id']:
            if user_role == 'publisher' and exhibition['co_organizer_type'] == 'publisher':
                # 检查是否属于协办方出版社
                cursor.execute("SELECT publisher_company_id FROM users WHERE user_id = %s", (user_id,))
                user_info = cursor.fetchone()
                return user_info and user_info['publisher_company_id'] == exhibition['co_organizer_id']
            elif user_role == 'dealer' and exhibition['co_organizer_type'] == 'dealer':
                # 检查是否属于协办方经销商
                cursor.execute("SELECT dealer_company_id FROM users WHERE user_id = %s", (user_id,))
                user_info = cursor.fetchone()
                return user_info and user_info['dealer_company_id'] == exhibition['co_organizer_id']

        return False

    @staticmethod
    def filter_contact_data(contact_data, is_confidential, can_view_confidential):
        """根据保密设置和用户权限过滤联系人数据"""
        if not is_confidential or can_view_confidential:
            # 不保密或有权限查看：返回联系人数据（不包含保密状态信息）
            return contact_data

        # 保密且用户无权查看，返回None表示不显示此联系人
        return None

def detect_login_type(login_input):
    """
    检测登录输入的类型

    Args:
        login_input: 用户输入的登录凭据

    Returns:
        str: 'email', 'phone', 'username'
    """
    if not login_input:
        return 'username'

    # 邮箱格式检测
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if re.match(email_pattern, login_input):
        return 'email'

    # 手机号格式检测（中国大陆手机号）
    phone_pattern = r'^1[3-9]\d{9}$'
    if re.match(phone_pattern, login_input):
        return 'phone'

    # 默认为用户名
    return 'username'

def validate_login_input(login_input, login_type):
    """
    验证登录输入的格式

    Args:
        login_input: 用户输入的登录凭据
        login_type: 登录类型

    Returns:
        tuple: (is_valid, error_message)
    """
    if not login_input or not login_input.strip():
        return False, "登录凭据不能为空"

    login_input = login_input.strip()

    if login_type == 'email':
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, login_input):
            return False, "邮箱格式不正确"
    elif login_type == 'phone':
        phone_pattern = r'^1[3-9]\d{9}$'
        if not re.match(phone_pattern, login_input):
            return False, "手机号格式不正确"
    elif login_type == 'username':
        # 用户名规则：3-20位，只能包含字母、数字、下划线
        username_pattern = r'^[a-zA-Z0-9_]{3,20}$'
        if not re.match(username_pattern, login_input):
            return False, "用户名格式不正确（3-20位字母、数字、下划线）"

    return True, ""

@common_bp.route('/validate_login_input', methods=['POST'])
def validate_login_input_api():
    """
    验证登录输入的格式和类型

    请求数据:
        login_input: 用户输入的登录凭据

    返回:
        登录类型和验证结果
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"code": 1, "message": "请求数据为空"})

        login_input = data.get('login_input', '').strip()

        if not login_input:
            return jsonify({
                "code": 1,
                "message": "登录凭据不能为空",
                "login_type": "username"
            })

        # 检测登录类型
        login_type = detect_login_type(login_input)

        # 验证格式
        is_valid, error_message = validate_login_input(login_input, login_type)

        if not is_valid:
            return jsonify({
                "code": 1,
                "message": error_message,
                "login_type": login_type
            })

        # 检查该登录凭据是否存在
        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                if login_type == 'email':
                    sql = "SELECT user_id, username FROM users WHERE email = %s"
                    cursor.execute(sql, (login_input,))
                elif login_type == 'phone':
                    sql = "SELECT user_id, username FROM users WHERE phone_number = %s"
                    cursor.execute(sql, (login_input,))
                else:  # username
                    sql = "SELECT user_id, username FROM users WHERE username = %s"
                    cursor.execute(sql, (login_input,))

                user = cursor.fetchone()

                return jsonify({
                    "code": 0,
                    "message": "验证成功",
                    "login_type": login_type,
                    "user_exists": bool(user),
                    "username": user['username'] if user else None
                })
        finally:
            connection.close()

    except Exception as e:
        return jsonify({
            "code": 1,
            "message": f"验证失败: {str(e)}",
            "login_type": "username"
        })

@common_bp.route('/login', methods=['POST'])
def login():
    username = request.form.get('username')
    password = request.form.get('password')

    if not username or not password:
        return jsonify({"message": "用户名和密码不能为空", "status": "fail"})

    connection = get_db_connection()

    try:
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            # 支持用户名、手机号、邮箱三种登录方式
            sql = "SELECT * FROM users WHERE (username = %s OR phone_number = %s OR email = %s)"
            cursor.execute(sql, (username, username, username))
            result = cursor.fetchone()

            if result and check_password_hash(result['password'], password):
                # 检测用户使用的登录方式
                login_type = detect_login_type(username)
                login_method = ""
                if login_type == 'email':
                    login_method = "邮箱"
                elif login_type == 'phone':
                    login_method = "手机号"
                else:
                    login_method = "用户名"

                # 将用户信息存储在session中
                session['user_id'] = result['user_id']
                session['username'] = result['username']
                session['role'] = result['role']
                session['login_method'] = login_method  # 记录登录方式

                # 根据角色获取更详细的信息
                user_role = result['role']
                user_id = result['user_id']

                # 获取显示名称
                display_name = result['username']  # 默认用户名

                if user_role == 'teacher' and result.get('name'):
                    display_name = result['name']  # 教师显示姓名

                elif user_role == 'publisher':
                    # 获取出版社名称
                    publisher_sql = """
                    SELECT pc.name
                    FROM publisher_companies pc
                    JOIN users u ON pc.id = u.publisher_company_id
                    WHERE u.user_id = %s
                    """
                    cursor.execute(publisher_sql, (user_id,))
                    publisher_info = cursor.fetchone()
                    if publisher_info and publisher_info['name']:
                        display_name = publisher_info['name']

                    # 检查出版社用户是否有目录，没有则创建默认目录（兼容旧版本）
                    try:
                        cursor.execute("SELECT COUNT(*) as count FROM directories WHERE publisher_id = %s", (user_id,))
                        directory_count = cursor.fetchone()

                        if directory_count and directory_count['count'] == 0:
                            # 没有目录，创建默认目录
                            cursor.execute("""
                                INSERT INTO directories (name, parent_id, publisher_id)
                                VALUES (%s, %s, %s)
                            """, ('默认目录', None, user_id))
                            connection.commit()
                            print(f"为出版社用户 {user_id} 创建默认目录成功 (登录时检测)")
                    except Exception as e:
                        print(f"为出版社用户 {user_id} 创建默认目录失败 (登录时检测): {str(e)}")
                        # 目录创建失败不影响登录成功

                elif user_role == 'dealer':
                    # 获取经销商名称
                    dealer_sql = """
                    SELECT dc.name
                    FROM dealer_companies dc
                    JOIN users u ON dc.id = u.dealer_company_id
                    WHERE u.user_id = %s
                    """
                    cursor.execute(dealer_sql, (user_id,))
                    dealer_info = cursor.fetchone()
                    if dealer_info and dealer_info['name']:
                        display_name = dealer_info['name']

                # 存储显示名称
                session['name'] = display_name

                # 记录登录成功日志
                AuditLogService.log_action(
                    action_type=AuditLogService.ActionType.LOGIN,
                    description=f"用户使用{login_method}登录成功",
                    details={
                        'login_method': login_method,
                        'username': result['username'],
                        'role': result['role'],
                        'display_name': display_name
                    }
                )

                return jsonify({
                    "message": f"使用{login_method}登录成功",
                    "status": "success",
                    "redirect": url_for('frontend.dashboard'),
                    "login_method": login_method,
                    "user_info": {
                        "username": result['username'],
                        "role": result['role'],
                        "display_name": display_name
                    }
                })
            else:
                # 记录登录失败日志
                login_type = detect_login_type(username)
                error_reason = ""

                if result:
                    error_reason = "密码错误"
                    # 记录登录失败日志（有用户记录但密码错误）
                    AuditLogService.log_action(
                        action_type=AuditLogService.ActionType.LOGIN,
                        result=AuditLogService.Result.FAILURE,
                        description="登录失败：密码错误",
                        details={
                            'attempted_username': username,
                            'login_type': login_type,
                            'error_reason': error_reason,
                            'user_exists': True
                        },
                        user_id=result['user_id']  # 用户存在时记录用户ID
                    )
                    return jsonify({"message": "密码错误，请检查后重试", "status": "fail"})
                else:
                    # 用户不存在
                    if login_type == 'email':
                        error_reason = "邮箱未注册"
                    elif login_type == 'phone':
                        error_reason = "手机号未注册"
                    else:
                        error_reason = "用户名不存在"

                    # 记录登录失败日志（用户不存在）
                    AuditLogService.log_action(
                        action_type=AuditLogService.ActionType.LOGIN,
                        result=AuditLogService.Result.FAILURE,
                        description=f"登录失败：{error_reason}",
                        details={
                            'attempted_username': username,
                            'login_type': login_type,
                            'error_reason': error_reason,
                            'user_exists': False
                        }
                    )

                    if login_type == 'email':
                        return jsonify({"message": "该邮箱未注册，请检查邮箱地址或先注册账号", "status": "fail"})
                    elif login_type == 'phone':
                        return jsonify({"message": "该手机号未注册，请检查手机号或先注册账号", "status": "fail"})
                    else:
                        return jsonify({"message": "该用户名不存在，请检查用户名或先注册账号", "status": "fail"})
    finally:
        connection.close()

@common_bp.route('/get_schools', methods=['GET'])
def get_schools():
    """
    获取学校列表，可选择只返回有展览的学校
    请求参数:
        has_exhibitions: 是否只返回有展览的学校（true/false）
    返回:
        学校列表
    """
    try:
        has_exhibitions = request.args.get('has_exhibitions', 'false').lower() == 'true'

        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        if has_exhibitions:
            query = """
            SELECT DISTINCT s.id, s.name
            FROM schools s
            JOIN book_exhibitions be ON s.id = be.school_id
            ORDER BY s.name
            """
        else:
            query = "SELECT id, name FROM schools ORDER BY name"

        cursor.execute(query)
        schools = cursor.fetchall()

        cursor.close()
        connection.close()

        return jsonify({"code": 0, "message": "获取成功", "data": schools})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取学校列表失败: {str(e)}"})

@common_bp.route('/get_user_info', methods=['GET'])
def get_user_info():
    """
    获取当前登录用户的信息
    返回:
        用户基本信息
    """
    if 'user_id' not in session:
        return jsonify({"status": "error", "message": "用户未登录"})

    try:
        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        user_id = session.get('user_id')
        user_role = session.get('role')

        # 根据角色获取用户详细信息
        query = """
        SELECT u.user_id, u.username, u.name, u.nickname, u.phone_number, u.contact_info, u.email, u.role,
               u.teacher_school_id, u.publisher_company_id, u.dealer_company_id
        FROM users u
        WHERE u.user_id = %s
        """
        cursor.execute(query, (user_id,))
        user_info = cursor.fetchone()

        if not user_info:
            return jsonify({"status": "error", "message": "用户不存在"})

        # 根据角色获取额外信息
        if user_role == 'publisher' and user_info.get('publisher_company_id'):
            query = "SELECT name FROM publisher_companies WHERE id = %s"
            cursor.execute(query, (user_info['publisher_company_id'],))
            company = cursor.fetchone()
            if company:
                user_info['company_name'] = company['name']

        elif user_role == 'dealer' and user_info.get('dealer_company_id'):
            query = "SELECT name FROM dealer_companies WHERE id = %s"
            cursor.execute(query, (user_info['dealer_company_id'],))
            company = cursor.fetchone()
            if company:
                user_info['company_name'] = company['name']

        elif user_role == 'teacher' and user_info.get('teacher_school_id'):
            query = "SELECT name FROM schools WHERE id = %s"
            cursor.execute(query, (user_info['teacher_school_id'],))
            school = cursor.fetchone()
            if school:
                user_info['school_name'] = school['name']

        cursor.close()
        connection.close()

        return jsonify({"status": "success", "user_info": user_info})
    except Exception as e:
        return jsonify({"status": "error", "message": f"获取用户信息失败: {str(e)}"})

@common_bp.route('/get_publisher_companies', methods=['GET'])
def get_publisher_companies():
    """
    获取所有出版社单位列表
    返回:
        出版社ID和名称列表
    """
    try:
        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 查询所有出版社单位
        query = "SELECT id, name FROM publisher_companies ORDER BY name"
        cursor.execute(query)
        companies = cursor.fetchall()

        cursor.close()
        connection.close()

        return jsonify({"code": 0, "message": "获取成功", "data": companies})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取出版社列表失败: {str(e)}"})

@common_bp.route('/get_dealer_companies', methods=['GET'])
def get_dealer_companies():
    """
    获取所有经销商单位列表
    返回:
        经销商ID和名称列表
    """
    try:
        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 查询所有经销商单位
        query = "SELECT id, name FROM dealer_companies ORDER BY name"
        cursor.execute(query)
        companies = cursor.fetchall()

        cursor.close()
        connection.close()

        return jsonify({"code": 0, "message": "获取成功", "data": companies})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取经销商列表失败: {str(e)}"})

@common_bp.route('/check_permission', methods=['GET'])
def check_permission():
    """
    检查当前用户是否具有特定权限
    请求参数:
        permission: 权限类型，如'register_exhibition'等
    返回:
        是否具有权限
    """
    if 'user_id' not in session:
        return jsonify({"status": "error", "message": "用户未登录"})

    try:
        permission = request.args.get('permission')
        user_id = session.get('user_id')
        user_role = session.get('role')

        if not permission:
            return jsonify({"status": "error", "message": "未指定权限类型"})

        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        has_permission = False

        # 根据角色和权限类型查询
        if user_role == 'publisher' and permission == 'register_exhibition':
            query = """
            SELECT pcp.can_register_exhibition
            FROM users u
            JOIN publisher_company_permissions pcp ON u.publisher_company_id = pcp.company_id
            WHERE u.user_id = %s
            """
            cursor.execute(query, (user_id,))
            result = cursor.fetchone()
            if result and result['can_register_exhibition'] == 1:
                has_permission = True

        elif user_role == 'dealer' and permission == 'initiate_exhibition':
            query = """
            SELECT dcp.can_initiate_exhibition
            FROM users u
            JOIN dealer_company_permissions dcp ON u.dealer_company_id = dcp.company_id
            WHERE u.user_id = %s
            """
            cursor.execute(query, (user_id,))
            result = cursor.fetchone()
            if result and result['can_initiate_exhibition'] == 1:
                has_permission = True

        cursor.close()
        connection.close()

        return jsonify({"status": "success", "has_permission": has_permission})
    except Exception as e:
        return jsonify({"status": "error", "message": f"检查权限失败: {str(e)}"})

@common_bp.route('/get_exhibitions', methods=['GET'])
def get_exhibitions():
    """
    获取书展活动列表
    请求参数:
        page: 页码，默认1
        limit: 每页数量，默认10
        tab: 标签筛选(all, published, upcoming, registered, ended)
        search: 搜索关键词
        start_date: 开始日期筛选
        end_date: 结束日期筛选
        school_id: 学校ID筛选
    返回:
        书展列表、状态统计、总数
    """
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "用户未登录"})

    try:
        user_id = session.get('user_id')
        user_role = session.get('role')

        # 获取请求参数
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 10))
        tab = request.args.get('tab', 'all')
        search = request.args.get('search', '')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        school_id = request.args.get('school_id', '')

        # 防止参数错误
        if page < 1:
            page = 1
        if limit < 1 or limit > 50:
            limit = 10

        offset = (page - 1) * limit

        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 构建查询条件
        where_clauses = []
        params = []

        # 基础条件 - 根据用户角色过滤可见书展
        # 普通用户只能看到已发布和已结束的书展
        # 协办方可以看到自己协办的待审核、已拒绝、已发布、已结束的书展
        user_role = session.get('role')

        # 获取用户所属组织ID（用于协办方权限判断）
        company_id = None
        co_organizer_type = None
        if user_role == 'publisher':
            cursor.execute("SELECT publisher_company_id FROM users WHERE user_id = %s", (user_id,))
            user_info = cursor.fetchone()
            company_id = user_info['publisher_company_id'] if user_info else None
            co_organizer_type = 'publisher'
        elif user_role == 'dealer':
            cursor.execute("SELECT dealer_company_id FROM users WHERE user_id = %s", (user_id,))
            user_info = cursor.fetchone()
            company_id = user_info['dealer_company_id'] if user_info else None
            co_organizer_type = 'dealer'

        # 构建可见性条件
        if company_id and co_organizer_type:
            # 协办方可以看到：1. 已发布/已结束的书展 2. 自己协办的待审核/已拒绝书展
            visibility_condition = f"""
                (be.status IN ('published', 'ended') OR
                 (be.co_organizer_type = '{co_organizer_type}' AND be.co_organizer_id = {company_id} AND be.status IN ('pending_review', 'rejected')))
            """
        else:
            # 普通用户只能看到已发布和已结束的书展
            visibility_condition = "be.status IN ('published', 'ended')"

        # 添加黑白名单可见性控制
        blacklist_condition = ""
        if company_id and co_organizer_type:
            blacklist_condition = f"""
                AND (
                    be.visibility_mode = 'all_visible' OR
                    (be.visibility_mode = 'blacklist' AND NOT EXISTS (
                        SELECT 1 FROM exhibition_organization_blacklist eob
                        WHERE eob.exhibition_id = be.id
                        AND eob.organization_type = '{co_organizer_type}'
                        AND eob.organization_id = {company_id}
                        AND eob.list_type = 'blacklist'
                    )) OR
                    (be.visibility_mode = 'whitelist' AND EXISTS (
                        SELECT 1 FROM exhibition_organization_blacklist eob
                        WHERE eob.exhibition_id = be.id
                        AND eob.organization_type = '{co_organizer_type}'
                        AND eob.organization_id = {company_id}
                        AND eob.list_type = 'whitelist'
                    ))
                )
            """

        # 将黑白名单条件添加到可见性条件中
        visibility_condition = f"({visibility_condition}){blacklist_condition}"

        if tab == 'all':
            where_clauses.append(visibility_condition)
        elif tab == 'published':
            where_clauses.append("be.status = 'published' AND be.start_time <= NOW() AND be.end_time > NOW()")
            where_clauses.append(visibility_condition)
        elif tab == 'upcoming':
            where_clauses.append("be.status = 'published' AND be.start_time > NOW() AND be.start_time <= DATE_ADD(NOW(), INTERVAL 3 DAY)")
            where_clauses.append(visibility_condition)
        elif tab == 'registerable':
            where_clauses.append("be.status = 'published' AND be.registration_deadline >= NOW()")
            where_clauses.append(visibility_condition)
            # 排除已报名的书展
            where_clauses.append("""
                NOT EXISTS (
                    SELECT 1 FROM exhibition_registrations er_check
                    WHERE er_check.exhibition_id = be.id
                    AND er_check.publisher_id = %s
                    AND er_check.status = 'registered'
                )
            """)
            params.append(user_id)
        elif tab == 'ended':
            where_clauses.append("be.status = 'ended' OR (be.status = 'published' AND be.end_time <= NOW())")
            where_clauses.append(visibility_condition)

        # 仅查询已报名的展览
        if tab == 'registered':
            where_clauses.append("er.publisher_id = %s AND er.status = 'registered'")
            where_clauses.append(visibility_condition)
            params.append(user_id)

        # 已取消的展览查询条件
        elif tab == 'cancelled':
            where_clauses.append("""
                EXISTS (
                    SELECT 1 FROM exhibition_registrations er_cancelled
                    WHERE er_cancelled.exhibition_id = be.id
                    AND er_cancelled.publisher_id = %s
                    AND er_cancelled.status = 'cancelled'
                )
                AND NOT EXISTS (
                    SELECT 1 FROM exhibition_registrations er_registered
                    WHERE er_registered.exhibition_id = be.id
                    AND er_registered.publisher_id = %s
                    AND er_registered.status = 'registered'
                )
            """)
            where_clauses.append(visibility_condition)
            params.append(user_id)
            params.append(user_id)

        # 关键词搜索
        if search:
            where_clauses.append("(be.title LIKE %s OR be.description LIKE %s OR s.name LIKE %s)")
            search_param = f'%{search}%'
            params.extend([search_param, search_param, search_param])

        # 日期筛选
        if start_date:
            where_clauses.append("be.start_time >= %s")
            params.append(start_date)
        if end_date:
            where_clauses.append("be.end_time <= %s")
            params.append(f"{end_date} 23:59:59")

        # 学校筛选
        if school_id and school_id != 'all':
            where_clauses.append("be.school_id = %s")
            params.append(school_id)

        # 构建WHERE子句
        where_sql = " AND ".join(where_clauses) if where_clauses else "1=1"

        # 查询书展列表
        if tab == 'registered':
            # 已报名展览需要JOIN报名表
            query = f"""
            SELECT be.id, be.title, be.logo_url, be.description, be.start_time, be.end_time,
                   be.registration_deadline, be.location, be.status, s.name as school_name,
                   ei.name as contact_name, ei.phone as contact_phone, be.created_at,
                   be.school_address, TRUE as is_registered
            FROM book_exhibitions be
            JOIN schools s ON be.school_id = s.id
            JOIN exhibition_initiators ei ON be.id = ei.exhibition_id
            JOIN exhibition_registrations er ON be.id = er.exhibition_id
            WHERE {where_sql}
            ORDER BY be.start_time DESC
            LIMIT %s, %s
            """
        elif tab == 'cancelled':
            # 已取消报名展览
            query = f"""
            SELECT be.id, be.title, be.logo_url, be.description, be.start_time, be.end_time,
                   be.registration_deadline, be.location, be.status, s.name as school_name,
                   ei.name as contact_name, ei.phone as contact_phone, be.created_at,
                   be.school_address, FALSE as is_registered
            FROM book_exhibitions be
            JOIN schools s ON be.school_id = s.id
            JOIN exhibition_initiators ei ON be.id = ei.exhibition_id
            WHERE {where_sql}
            ORDER BY be.start_time DESC
            LIMIT %s, %s
            """
        else:
            # 正常的展览列表需要LEFT JOIN报名表，以判断当前用户是否已报名
            query = f"""
            SELECT be.id, be.title, be.logo_url, be.description, be.start_time, be.end_time,
                   be.registration_deadline, be.location, be.status, s.name as school_name,
                   ei.name as contact_name, ei.phone as contact_phone, be.created_at,
                   be.school_address, CASE WHEN er.id IS NOT NULL THEN TRUE ELSE FALSE END as is_registered
            FROM book_exhibitions be
            JOIN schools s ON be.school_id = s.id
            JOIN exhibition_initiators ei ON be.id = ei.exhibition_id
            LEFT JOIN exhibition_registrations er ON be.id = er.exhibition_id AND er.publisher_id = %s AND er.status = 'registered'
            WHERE {where_sql}
            ORDER BY be.start_time DESC
            LIMIT %s, %s
            """
            # 所有非注册标签查询都需要添加用户ID用于判断是否报名
            if tab != 'registered':
                params.insert(0, user_id)

        params.append(offset)
        params.append(limit)

        cursor.execute(query, params)
        exhibitions = cursor.fetchall()

        # 格式化时间
        for exhibition in exhibitions:
            exhibition['start_time'] = exhibition['start_time'].strftime('%Y-%m-%d %H:%M') if exhibition['start_time'] else ''
            exhibition['end_time'] = exhibition['end_time'].strftime('%Y-%m-%d %H:%M') if exhibition['end_time'] else ''
            exhibition['registration_deadline'] = exhibition['registration_deadline'].strftime('%Y-%m-%d %H:%M') if exhibition['registration_deadline'] else ''
            exhibition['created_at'] = exhibition['created_at'].strftime('%Y-%m-%d') if exhibition['created_at'] else ''

        # 查询总数
        count_params = params[:-2]  # 去掉 LIMIT 相关参数
        if tab == 'registered':
            count_query = f"""
            SELECT COUNT(be.id) as total
            FROM book_exhibitions be
            JOIN schools s ON be.school_id = s.id
            JOIN exhibition_initiators ei ON be.id = ei.exhibition_id
            JOIN exhibition_registrations er ON be.id = er.exhibition_id
            WHERE {where_sql}
            """
        elif tab == 'cancelled':
            count_query = f"""
            SELECT COUNT(be.id) as total
            FROM book_exhibitions be
            JOIN schools s ON be.school_id = s.id
            JOIN exhibition_initiators ei ON be.id = ei.exhibition_id
            WHERE {where_sql}
            """
        else:
            count_query = f"""
            SELECT COUNT(be.id) as total
            FROM book_exhibitions be
            JOIN schools s ON be.school_id = s.id
            JOIN exhibition_initiators ei ON be.id = ei.exhibition_id
            LEFT JOIN exhibition_registrations er ON be.id = er.exhibition_id AND er.publisher_id = %s AND er.status = 'registered'
            WHERE {where_sql}
            """

        cursor.execute(count_query, count_params)
        total = cursor.fetchone()['total']

        # 查询各状态展览数量（应用黑白名单过滤）
        status_counts = {}

        # 构建黑白名单条件用于统计查询
        blacklist_condition_for_stats = ""
        if company_id and co_organizer_type:
            blacklist_condition_for_stats = f"""
                AND (
                    be.visibility_mode = 'all_visible' OR
                    (be.visibility_mode = 'blacklist' AND NOT EXISTS (
                        SELECT 1 FROM exhibition_organization_blacklist eob
                        WHERE eob.exhibition_id = be.id
                        AND eob.organization_type = '{co_organizer_type}'
                        AND eob.organization_id = {company_id}
                        AND eob.list_type = 'blacklist'
                    )) OR
                    (be.visibility_mode = 'whitelist' AND EXISTS (
                        SELECT 1 FROM exhibition_organization_blacklist eob
                        WHERE eob.exhibition_id = be.id
                        AND eob.organization_type = '{co_organizer_type}'
                        AND eob.organization_id = {company_id}
                        AND eob.list_type = 'whitelist'
                    ))
                )
            """

        # 全部
        cursor.execute(f"SELECT COUNT(*) as count FROM book_exhibitions be WHERE be.status IN ('published', 'ended'){blacklist_condition_for_stats}")
        status_counts['all'] = cursor.fetchone()['count']

        # 进行中 - 已开始但未结束的活动
        cursor.execute(f"SELECT COUNT(*) as count FROM book_exhibitions be WHERE be.status = 'published' AND be.start_time <= NOW() AND be.end_time > NOW(){blacklist_condition_for_stats}")
        status_counts['published'] = cursor.fetchone()['count']

        # 即将开始 - 三天内开始的活动
        cursor.execute(f"SELECT COUNT(*) as count FROM book_exhibitions be WHERE be.status = 'published' AND be.start_time > NOW() AND be.start_time <= DATE_ADD(NOW(), INTERVAL 3 DAY){blacklist_condition_for_stats}")
        status_counts['upcoming'] = cursor.fetchone()['count']

        # 可报名 - 报名截止时间尚未到且用户未报名的活动
        cursor.execute(f"""
        SELECT COUNT(*) as count
        FROM book_exhibitions be
        WHERE be.status = 'published'
        AND be.registration_deadline >= NOW()
        AND NOT EXISTS (
            SELECT 1 FROM exhibition_registrations er
            WHERE er.exhibition_id = be.id
            AND er.publisher_id = %s
            AND er.status = 'registered'
        ){blacklist_condition_for_stats}
        """, (user_id,))
        status_counts['registerable'] = cursor.fetchone()['count']

        # 已结束 - 包括手动结束或时间已过的活动
        cursor.execute(f"SELECT COUNT(*) as count FROM book_exhibitions be WHERE (be.status = 'ended' OR (be.status = 'published' AND be.end_time <= NOW())){blacklist_condition_for_stats}")
        status_counts['ended'] = cursor.fetchone()['count']

        # 已报名
        cursor.execute(f"""
        SELECT COUNT(DISTINCT be.id) as count
        FROM book_exhibitions be
        JOIN exhibition_registrations er ON be.id = er.exhibition_id
        WHERE er.publisher_id = %s AND er.status = 'registered'{blacklist_condition_for_stats}
        """, (user_id,))
        status_counts['registered'] = cursor.fetchone()['count']

        # 已取消的报名
        cursor.execute(f"""
        SELECT COUNT(DISTINCT be.id) as count
        FROM book_exhibitions be
        WHERE EXISTS (
            SELECT 1 FROM exhibition_registrations er_cancelled
            WHERE er_cancelled.exhibition_id = be.id
            AND er_cancelled.publisher_id = %s
            AND er_cancelled.status = 'cancelled'
        )
        AND NOT EXISTS (
            SELECT 1 FROM exhibition_registrations er_registered
            WHERE er_registered.exhibition_id = be.id
            AND er_registered.publisher_id = %s
            AND er_registered.status = 'registered'
        ){blacklist_condition_for_stats}
        """, (user_id, user_id))
        status_counts['cancelled'] = cursor.fetchone()['count']

        cursor.close()
        connection.close()

        result = {
            "exhibitions": exhibitions,
            "status_counts": status_counts,
            "total": total
        }

        return jsonify({"code": 0, "message": "获取成功", "data": result})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取书展列表失败: {str(e)}"})

@common_bp.route('/get_exhibition_detail', methods=['GET'])
def get_exhibition_detail():
    """
    获取书展详情
    请求参数:
        id: 书展ID
    返回:
        书展详细信息
    """
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "用户未登录"})

    try:
        exhibition_id = request.args.get('id')
        if not exhibition_id:
            return jsonify({"code": 1, "message": "未提供书展ID"})

        user_id = session.get('user_id')

        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 获取用户所属组织ID（用于协办方权限判断和黑白名单控制）
        user_role = session.get('role')
        company_id = None
        co_organizer_type = None
        if user_role == 'publisher':
            cursor.execute("SELECT publisher_company_id FROM users WHERE user_id = %s", (user_id,))
            user_info = cursor.fetchone()
            company_id = user_info['publisher_company_id'] if user_info else None
            co_organizer_type = 'publisher'
        elif user_role == 'dealer':
            cursor.execute("SELECT dealer_company_id FROM users WHERE user_id = %s", (user_id,))
            user_info = cursor.fetchone()
            company_id = user_info['dealer_company_id'] if user_info else None
            co_organizer_type = 'dealer'

        # 构建黑白名单可见性条件
        blacklist_condition = ""
        if company_id and co_organizer_type:
            blacklist_condition = f"""
                AND (
                    be.visibility_mode = 'all_visible' OR
                    (be.visibility_mode = 'blacklist' AND NOT EXISTS (
                        SELECT 1 FROM exhibition_organization_blacklist eob
                        WHERE eob.exhibition_id = be.id
                        AND eob.organization_type = '{co_organizer_type}'
                        AND eob.organization_id = {company_id}
                        AND eob.list_type = 'blacklist'
                    )) OR
                    (be.visibility_mode = 'whitelist' AND EXISTS (
                        SELECT 1 FROM exhibition_organization_blacklist eob
                        WHERE eob.exhibition_id = be.id
                        AND eob.organization_type = '{co_organizer_type}'
                        AND eob.organization_id = {company_id}
                        AND eob.list_type = 'whitelist'
                    ))
                )
            """

        # 查询书展详情
        query = f"""
        SELECT be.id, be.title, be.logo_url, be.description, be.start_time, be.end_time,
               be.registration_deadline, be.location, be.status, be.requires_campus_registration,
               be.registration_requirements, be.registration_qrcode, be.allows_parking, be.requirements,
               be.license_plate_required, s.name as school_name, s.id as school_id, be.school_address,
               be.co_organizer_type, be.co_organizer_id, be.initiator_id,
               CASE WHEN er.id IS NOT NULL THEN TRUE ELSE FALSE END as is_registered
        FROM book_exhibitions be
        JOIN schools s ON be.school_id = s.id
        LEFT JOIN exhibition_registrations er ON be.id = er.exhibition_id
                                           AND er.publisher_id = %s
                                           AND er.status = 'registered'
        WHERE be.id = %s AND (
            be.status IN ('published', 'ended') OR
            be.initiator_id = %s OR
            (be.co_organizer_type = %s AND be.co_organizer_id = %s AND be.status IN ('pending_review', 'rejected'))
        ){blacklist_condition}
        """

        cursor.execute(query, (user_id, exhibition_id, user_id, co_organizer_type or '', company_id or 0))
        exhibition = cursor.fetchone()

        if not exhibition:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "书展不存在或无权查看"})

        # 格式化时间
        exhibition['start_time'] = exhibition['start_time'].strftime('%Y-%m-%d %H:%M') if exhibition['start_time'] else ''
        exhibition['end_time'] = exhibition['end_time'].strftime('%Y-%m-%d %H:%M') if exhibition['end_time'] else ''
        exhibition['registration_deadline'] = exhibition['registration_deadline'].strftime('%Y-%m-%d %H:%M') if exhibition['registration_deadline'] else ''

        # 判断是否为发起者
        exhibition['is_initiator'] = exhibition['initiator_id'] == user_id

        # 查询发起人信息
        query = """
        SELECT name, phone, department, position, email
        FROM exhibition_initiators
        WHERE exhibition_id = %s
        """
        cursor.execute(query, (exhibition_id,))
        initiator = cursor.fetchone()

        if initiator:
            exhibition['initiator'] = initiator
        else:
            exhibition['initiator'] = {
                'name': '未知',
                'phone': '未知'
            }

        # 协办方联系人信息将在后面应用保密规则后设置

        # 判断当前用户是否为协办方审核者
        exhibition['is_co_organizer_reviewer'] = False
        exhibition['is_co_organizer'] = False
        if exhibition['co_organizer_type'] and exhibition['co_organizer_id']:
            if user_role == 'publisher' and exhibition['co_organizer_type'] == 'publisher':
                if company_id == exhibition['co_organizer_id']:
                    exhibition['is_co_organizer'] = True
                    if exhibition['status'] == 'pending_review':
                        exhibition['is_co_organizer_reviewer'] = True
            elif user_role == 'dealer' and exhibition['co_organizer_type'] == 'dealer':
                if company_id == exhibition['co_organizer_id']:
                    exhibition['is_co_organizer'] = True
                    if exhibition['status'] == 'pending_review':
                        exhibition['is_co_organizer_reviewer'] = True

        # 如果是发起者或协办方，查询参展单位信息
        if exhibition['is_initiator'] or exhibition['is_co_organizer']:
            # 统一的单位名称显示逻辑（前端根据用户角色决定显示格式）
            company_name_sql = """
                CASE
                    WHEN er.is_proxy_registration = 1 AND er.partner_supplier_id IS NOT NULL
                    THEN partner_pc.name
                    WHEN u.role = 'publisher' THEN pc.name
                    WHEN u.role = 'dealer' THEN dc.name
                    ELSE COALESCE(pc.name, dc.name)
                END AS company_name
            """

            cursor.execute(f"""
                SELECT
                    er.id, er.status, er.created_at,
                    u.name AS publisher_name,
                    {company_name_sql},
                    (SELECT COUNT(*) FROM exhibition_participants ep WHERE ep.registration_id = er.id) AS participants_count,
                    er.is_proxy_registration,
                    er.partner_supplier_id,
                    dc.name as dealer_company_name,
                    partner_pc.name as supplier_company_name
                FROM exhibition_registrations er
                JOIN users u ON er.publisher_id = u.user_id
                LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id
                LEFT JOIN dealer_companies dc ON u.dealer_company_id = dc.id
                LEFT JOIN publisher_companies partner_pc ON er.partner_supplier_id = partner_pc.id
                WHERE er.exhibition_id = %s AND er.status = 'registered'
                ORDER BY er.created_at DESC
            """, (exhibition_id,))
            registrations = cursor.fetchall()

            # 格式化时间
            for reg in registrations:
                reg['created_at'] = reg['created_at'].strftime('%Y-%m-%d %H:%M') if reg['created_at'] else ''

            exhibition['registrations'] = registrations
        else:
            exhibition['registrations'] = []

        cursor.close()
        connection.close()

        return jsonify({"code": 0, "message": "获取成功", "data": exhibition})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取书展详情失败: {str(e)}"})

@common_bp.route('/get_exhibition_detail/<int:exhibition_id>', methods=['GET'])
def get_exhibition_detail_by_id(exhibition_id):
    """
    通过路径参数获取书展详情
    路径参数:
        exhibition_id: 书展ID
    返回:
        书展详细信息
    """
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "用户未登录"})

    try:
        user_id = session.get('user_id')

        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 获取用户所属组织ID（用于黑白名单控制）
        user_role = session.get('role')
        company_id = None
        co_organizer_type = None
        if user_role == 'publisher':
            cursor.execute("SELECT publisher_company_id FROM users WHERE user_id = %s", (user_id,))
            user_info = cursor.fetchone()
            company_id = user_info['publisher_company_id'] if user_info else None
            co_organizer_type = 'publisher'
        elif user_role == 'dealer':
            cursor.execute("SELECT dealer_company_id FROM users WHERE user_id = %s", (user_id,))
            user_info = cursor.fetchone()
            company_id = user_info['dealer_company_id'] if user_info else None
            co_organizer_type = 'dealer'

        # 构建黑白名单可见性条件
        blacklist_condition = ""
        if company_id and co_organizer_type:
            blacklist_condition = f"""
                AND (
                    be.visibility_mode = 'all_visible' OR
                    (be.visibility_mode = 'blacklist' AND NOT EXISTS (
                        SELECT 1 FROM exhibition_organization_blacklist eob
                        WHERE eob.exhibition_id = be.id
                        AND eob.organization_type = '{co_organizer_type}'
                        AND eob.organization_id = {company_id}
                        AND eob.list_type = 'blacklist'
                    )) OR
                    (be.visibility_mode = 'whitelist' AND EXISTS (
                        SELECT 1 FROM exhibition_organization_blacklist eob
                        WHERE eob.exhibition_id = be.id
                        AND eob.organization_type = '{co_organizer_type}'
                        AND eob.organization_id = {company_id}
                        AND eob.list_type = 'whitelist'
                    ))
                )
            """

        # 查询书展详情
        query = f"""
        SELECT be.id, be.title, be.logo_url, be.description, be.start_time, be.end_time,
               be.registration_deadline, be.location, be.status, be.requires_campus_registration,
               be.registration_requirements, be.registration_qrcode, be.allows_parking, be.requirements,
               be.license_plate_required, s.name as school_name, s.id as school_id, be.school_address,
               be.co_organizer_type, be.co_organizer_id, be.initiator_id,
               (be.initiator_id = %s) as is_initiator,
               CASE WHEN er.id IS NOT NULL THEN TRUE ELSE FALSE END as is_registered
        FROM book_exhibitions be
        JOIN schools s ON be.school_id = s.id
        LEFT JOIN exhibition_registrations er ON be.id = er.exhibition_id
                                           AND er.publisher_id = %s
                                           AND er.status = 'registered'
        WHERE be.id = %s AND be.status IN ('published', 'ended'){blacklist_condition}
        """
        cursor.execute(query, (user_id, user_id, exhibition_id))
        exhibition = cursor.fetchone()

        if not exhibition:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "书展不存在或无权查看"})

        # 格式化时间
        exhibition['start_time'] = exhibition['start_time'].strftime('%Y-%m-%d %H:%M') if exhibition['start_time'] else ''
        exhibition['end_time'] = exhibition['end_time'].strftime('%Y-%m-%d %H:%M') if exhibition['end_time'] else ''
        exhibition['registration_deadline'] = exhibition['registration_deadline'].strftime('%Y-%m-%d %H:%M') if exhibition['registration_deadline'] else ''

        # 查询协办方指定的联系人信息
        cursor.execute("""
            SELECT c.id, c.name, c.phone, c.company_name, c.position, c.email
            FROM exhibition_contacts ec
            JOIN co_organizer_contacts c ON ec.contact_id = c.id
            WHERE ec.exhibition_id = %s AND c.is_active = 1
            ORDER BY c.name
        """, (exhibition_id,))
        all_co_organizer_contacts = cursor.fetchall()

        # 检查当前用户是否有权查看保密联系人信息
        can_view_confidential = ContactPrivacyService.check_confidential_permission(
            cursor, user_id, user_role, exhibition_id
        )

        # 过滤协办方联系人（应用保密规则）
        co_organizer_contacts = []
        for contact in all_co_organizer_contacts:
            # 检查联系人是否保密
            cursor.execute("""
                SELECT is_confidential
                FROM exhibition_contact_privacy
                WHERE exhibition_id = %s AND contact_type = 'co_organizer' AND contact_id = %s
            """, (exhibition_id, contact['id']))

            privacy_result = cursor.fetchone()
            is_confidential = privacy_result['is_confidential'] if privacy_result else False

            # 如果不保密或用户有权查看，则包含此联系人
            if not is_confidential or can_view_confidential:
                co_organizer_contacts.append(contact)

        # 查询主办方联系人信息
        cursor.execute("""
            SELECT name, phone, department, position, email
            FROM exhibition_initiators
            WHERE exhibition_id = %s
        """, (exhibition_id,))
        initiator = cursor.fetchone()

        # 处理主办方联系人保密
        initiator_data = None
        if initiator:
            # 检查主办方联系人是否保密
            cursor.execute("""
                SELECT is_confidential
                FROM exhibition_contact_privacy
                WHERE exhibition_id = %s AND contact_type = 'initiator' AND contact_id = %s
            """, (exhibition_id, exhibition_id))

            privacy_result = cursor.fetchone()
            is_confidential = privacy_result['is_confidential'] if privacy_result else False

            # 如果不保密或用户有权查看，则包含主办方联系人
            if not is_confidential or can_view_confidential:
                initiator_data = initiator

        # 设置联系人信息到exhibition对象
        exhibition['co_organizer_contacts'] = co_organizer_contacts
        exhibition['initiator'] = initiator_data

        # 设置主要联系人信息（用于向后兼容）
        if initiator_data:
            exhibition['contact_name'] = initiator_data['name']
            exhibition['contact_phone'] = initiator_data['phone']
            exhibition['contact_email'] = initiator_data.get('email', '')
            exhibition['contact_department'] = initiator_data.get('department', '')
            exhibition['contact_position'] = initiator_data.get('position', '')
        elif co_organizer_contacts:
            # 如果主办方联系人保密但有可见的协办方联系人，使用第一个协办方联系人
            main_contact = co_organizer_contacts[0]
            exhibition['contact_name'] = main_contact['name']
            exhibition['contact_phone'] = main_contact['phone']
            exhibition['contact_email'] = main_contact.get('email', '')
            exhibition['contact_department'] = main_contact.get('position', '')
            exhibition['contact_position'] = main_contact.get('position', '')
        else:
            # 所有联系人都保密且用户无权查看，不显示任何联系人信息
            exhibition['contact_name'] = ''
            exhibition['contact_phone'] = ''
            exhibition['contact_email'] = ''
            exhibition['contact_department'] = ''
            exhibition['contact_position'] = ''

        cursor.close()
        connection.close()

        return jsonify({"code": 0, "message": "获取成功", "data": exhibition})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取书展详情失败: {str(e)}"})

@common_bp.route('/get_unregistered_companies', methods=['GET'])
def get_unregistered_companies():
    """
    获取书展中“有报名权限但未参展”的单位列表
    请求参数:
        exhibition_id: 书展ID (必填)
        org_type: 组织类型，可选 'publisher' 或 'dealer'；不传则返回两类
        search: 搜索关键词（按单位名称模糊匹配）
    权限:
        仅书展发起人或该书展协办方有权查询
    返回:
        { publisher: [...], dealer: [...] } 或 { list: [...], type: 'publisher'|'dealer' }
    """
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "用户未登录"})

    exhibition_id = request.args.get('exhibition_id')
    org_type = request.args.get('org_type', '').strip()
    search = request.args.get('search', '').strip()

    if not exhibition_id:
        return jsonify({"code": 1, "message": "未提供书展ID"})

    try:
        user_id = session.get('user_id')
        user_role = session.get('role')

        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 读取书展信息（含可见性控制）
        cursor.execute("""
            SELECT id, initiator_id, visibility_mode, co_organizer_type, co_organizer_id
            FROM book_exhibitions
            WHERE id = %s
        """, (exhibition_id,))
        exhibition = cursor.fetchone()
        if not exhibition:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "书展不存在"})

        # 权限校验：发起人或协办方公司
        has_access = False
        if exhibition['initiator_id'] == user_id:
            has_access = True
        else:
            if user_role == 'publisher':
                cursor.execute("SELECT publisher_company_id FROM users WHERE user_id = %s", (user_id,))
                info = cursor.fetchone()
                if info and exhibition['co_organizer_type'] == 'publisher' and info['publisher_company_id'] == exhibition['co_organizer_id']:
                    has_access = True
            elif user_role == 'dealer':
                cursor.execute("SELECT dealer_company_id FROM users WHERE user_id = %s", (user_id,))
                info = cursor.fetchone()
                if info and exhibition['co_organizer_type'] == 'dealer' and info['dealer_company_id'] == exhibition['co_organizer_id']:
                    has_access = True
        if not has_access:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "无权限查看未参展单位"})

        # 构造可见性过滤片段
        def build_visibility_clause(kind_alias, kind_name):
            # kind_alias: 表别名字段名，如 pc.id / dc.id
            # kind_name: 'publisher' | 'dealer'
            if exhibition['visibility_mode'] == 'whitelist':
                return f"""
                    AND EXISTS (
                        SELECT 1 FROM exhibition_organization_blacklist eob
                        WHERE eob.exhibition_id = %s
                          AND eob.organization_type = '{kind_name}'
                          AND eob.organization_id = {kind_alias}
                          AND eob.list_type = 'whitelist'
                    )
                """
            elif exhibition['visibility_mode'] == 'blacklist':
                return f"""
                    AND NOT EXISTS (
                        SELECT 1 FROM exhibition_organization_blacklist eob
                        WHERE eob.exhibition_id = %s
                          AND eob.organization_type = '{kind_name}'
                          AND eob.organization_id = {kind_alias}
                          AND eob.list_type = 'blacklist'
                    )
                """
            else:
                return ""

        params_common = []
        # 搜索参数预处理
        search_like = f"%{search}%" if search else None

        result = {}

        def query_publishers():
            vis_clause = build_visibility_clause('pc.id', 'publisher')
            sql = f"""
                SELECT pc.id, pc.name
                FROM publisher_companies pc
                JOIN publisher_company_permissions pcp ON pc.id = pcp.company_id AND pcp.can_register_exhibition = 1
                WHERE 1=1
                    {{search_cond}}
                    {vis_clause}
                    AND NOT EXISTS (
                        SELECT 1 FROM exhibition_registrations er
                        JOIN users u ON er.publisher_id = u.user_id
                        WHERE er.exhibition_id = %s
                          AND er.status = 'registered'
                          AND u.publisher_company_id = pc.id
                    )
                ORDER BY pc.name
            """
            params = []
            # 可见性参数
            if exhibition['visibility_mode'] in ['whitelist', 'blacklist']:
                params.append(exhibition_id)
            # 搜索
            if search_like:
                sql = sql.replace('{search_cond}', 'AND pc.name LIKE %s')
                params.append(search_like)
            else:
                sql = sql.replace('{search_cond}', '')
            params.append(exhibition_id)
            cursor.execute(sql, tuple(params))
            return cursor.fetchall()

        def query_dealers():
            vis_clause = build_visibility_clause('dc.id', 'dealer')
            sql = f"""
                SELECT dc.id, dc.name
                FROM dealer_companies dc
                JOIN dealer_company_permissions dcp ON dc.id = dcp.company_id AND dcp.can_register_exhibition = 1
                WHERE 1=1
                    {{search_cond}}
                    {vis_clause}
                    AND NOT EXISTS (
                        SELECT 1 FROM exhibition_registrations er
                        JOIN users u ON er.publisher_id = u.user_id
                        WHERE er.exhibition_id = %s
                          AND er.status = 'registered'
                          AND u.dealer_company_id = dc.id
                    )
                ORDER BY dc.name
            """
            params = []
            if exhibition['visibility_mode'] in ['whitelist', 'blacklist']:
                params.append(exhibition_id)
            if search_like:
                sql = sql.replace('{search_cond}', 'AND dc.name LIKE %s')
                params.append(search_like)
            else:
                sql = sql.replace('{search_cond}', '')
            params.append(exhibition_id)
            cursor.execute(sql, tuple(params))
            return cursor.fetchall()

        if org_type in ['publisher', 'dealer']:
            if org_type == 'publisher':
                data_list = query_publishers()
            else:
                data_list = query_dealers()
            payload = {"list": data_list, "type": org_type}
        else:
            payload = {
                "publisher": query_publishers(),
                "dealer": query_dealers()
            }

        cursor.close()
        connection.close()
        return jsonify({"code": 0, "message": "获取成功", "data": payload})

    except Exception as e:
        return jsonify({"code": 1, "message": f"获取未参展单位失败: {str(e)}"})


@common_bp.route('/register_exhibition', methods=['POST'])
def register_exhibition():
    """
    报名参加书展
    请求数据:
        exhibition_id: 书展ID
        participants: [{name, phone, role, is_contact}, ...]
    返回:
        报名结果
    """
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "用户未登录"})

    try:
        data = request.get_json()
        if not data:
            return jsonify({"code": 1, "message": "请求数据为空"})

        exhibition_id = data.get('exhibition_id')
        participants = data.get('participants', [])

        if not exhibition_id:
            return jsonify({"code": 1, "message": "未提供书展ID"})
        if not participants:
            return jsonify({"code": 1, "message": "未提供参展人员信息"})

        user_id = session.get('user_id')

        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 获取用户所属组织ID（用于黑白名单控制）
        user_role = session.get('role')
        company_id = None
        co_organizer_type = None
        if user_role == 'publisher':
            cursor.execute("SELECT publisher_company_id FROM users WHERE user_id = %s", (user_id,))
            user_info = cursor.fetchone()
            company_id = user_info['publisher_company_id'] if user_info else None
            co_organizer_type = 'publisher'
        elif user_role == 'dealer':
            cursor.execute("SELECT dealer_company_id FROM users WHERE user_id = %s", (user_id,))
            user_info = cursor.fetchone()
            company_id = user_info['dealer_company_id'] if user_info else None
            co_organizer_type = 'dealer'

        # 构建黑白名单可见性条件
        blacklist_condition = ""
        if company_id and co_organizer_type:
            blacklist_condition = f"""
                AND (
                    be.visibility_mode = 'all_visible' OR
                    (be.visibility_mode = 'blacklist' AND NOT EXISTS (
                        SELECT 1 FROM exhibition_organization_blacklist eob
                        WHERE eob.exhibition_id = be.id
                        AND eob.organization_type = '{co_organizer_type}'
                        AND eob.organization_id = {company_id}
                        AND eob.list_type = 'blacklist'
                    )) OR
                    (be.visibility_mode = 'whitelist' AND EXISTS (
                        SELECT 1 FROM exhibition_organization_blacklist eob
                        WHERE eob.exhibition_id = be.id
                        AND eob.organization_type = '{co_organizer_type}'
                        AND eob.organization_id = {company_id}
                        AND eob.list_type = 'whitelist'
                    ))
                )
            """

        # 检查书展是否存在及状态，同时进行黑白名单检查
        query = f"""
        SELECT be.id, be.status, be.start_time, be.end_time, be.registration_deadline, be.visibility_mode
        FROM book_exhibitions be
        WHERE be.id = %s AND be.status IN ('published', 'ended'){blacklist_condition}
        """
        cursor.execute(query, (exhibition_id,))
        exhibition = cursor.fetchone()

        if not exhibition:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "书展不存在或您无权访问此书展"})

        # 检查是否已过报名截止时间
        now = datetime.datetime.now()
        if exhibition['registration_deadline'] and exhibition['registration_deadline'] < now:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "报名已截止，无法完成报名"})

        # 验证联系人信息
        contact_person = None
        for participant in participants:
            if participant.get('is_contact'):
                contact_person = participant
                break

        if not contact_person:
            return jsonify({"code": 1, "message": "请指定一名联系人"})

        if not contact_person.get('phone'):
            return jsonify({"code": 1, "message": "联系人的手机号是必填项"})

        # 验证联系人手机号格式
        phone_regex = re.compile(r'^1[3-9]\d{9}$')
        if not phone_regex.match(contact_person.get('phone')):
            return jsonify({"code": 1, "message": "联系人手机号格式不正确"})

        # 验证其他参与者非空手机号的格式
        for participant in participants:
            if not participant.get('is_contact') and participant.get('phone') and not phone_regex.match(participant.get('phone')):
                return jsonify({"code": 1, "message": "参展人员手机号格式不正确"})

        # 验证车牌号码要求：如果活动要求填写车牌，则至少需要一人填写或全团队无车
        if exhibition.get('license_plate_required'):
            has_license_plate = any(p.get('license_plate') and p.get('license_plate').strip() for p in participants)
            # 检查是否为全团队无车（所有人车牌号都为空）
            all_empty_license_plates = all(not p.get('license_plate') or not p.get('license_plate').strip() for p in participants)

            # 如果没有人填写车牌号且不是全团队无车，则报错
            if not has_license_plate and not all_empty_license_plates:
                return jsonify({"code": 1, "message": "活动要求必须填写车牌号，请填写至少一个车牌号或选择整个团队无车参展"})

            # 如果全团队无车，允许通过（all_empty_license_plates = True 表示全团队无车）

        if user_role != 'publisher' and user_role != 'dealer':
            return jsonify({"code": 1, "message": "只有出版社/供应商可以报名参展"})

        # 检查是否有权限
        # 不同角色用不同的语句判断
        if user_role == 'publisher':
            query = """
            SELECT pcp.can_register_exhibition
            FROM users u
            JOIN publisher_company_permissions pcp ON u.publisher_company_id = pcp.company_id
            WHERE u.user_id = %s
            """
            cursor.execute(query, (user_id,))
        elif user_role == 'dealer':
            query = """
            SELECT dcp.can_register_exhibition
            FROM users u
            JOIN dealer_company_permissions dcp ON u.dealer_company_id = dcp.company_id
            WHERE u.user_id = %s
            """
            cursor.execute(query, (user_id,))

        permission = cursor.fetchone()

        if not permission or permission['can_register_exhibition'] != 1:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "您没有书展报名权限"})

        # 检查书展是否已开始
        if exhibition['start_time'] and exhibition['start_time'] <= now:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "书展已开始，无法修改报名信息"})

        # 创建或更新报名记录
        try:
            connection.begin()

            # 检查是否已经报名
            query = """
            SELECT id, status FROM exhibition_registrations
            WHERE exhibition_id = %s AND publisher_id = %s
            """
            cursor.execute(query, (exhibition_id, user_id))
            existing_registration = cursor.fetchone()

            if existing_registration:
                # 如果已取消报名，则更新状态
                if existing_registration['status'] == 'cancelled':
                    query = """
                    UPDATE exhibition_registrations
                    SET status = 'registered', updated_at = NOW()
                    WHERE id = %s
                    """
                    cursor.execute(query, (existing_registration['id'],))

                # 删除旧的参展人员记录
                query = """
                DELETE FROM exhibition_participants
                WHERE registration_id = %s
                """
                cursor.execute(query, (existing_registration['id'],))

                registration_id = existing_registration['id']
                action_message = "更新报名信息成功"
            else:
                # 插入新报名
                query = """
                INSERT INTO exhibition_registrations (exhibition_id, publisher_id, status, created_at)
                VALUES (%s, %s, 'registered', NOW())
                """
                cursor.execute(query, (exhibition_id, user_id))
                registration_id = connection.insert_id()
                action_message = "报名成功"

            # 插入参展人员
            for participant in participants:
                query = """
                INSERT INTO exhibition_participants
                (registration_id, name, phone, role, license_plate, is_contact, created_at)
                VALUES (%s, %s, %s, %s, %s, %s, NOW())
                """
                cursor.execute(query, (
                    registration_id,
                    participant.get('name'),
                    participant.get('phone', ''),  # 确保手机号为空时使用空字符串
                    participant.get('role', ''),
                    participant.get('license_plate', ''),  # 车牌号码
                    1 if participant.get('is_contact') else 0
                ))

            connection.commit()

            # 发送邮件通知教师
            try:
                from app.services.exhibition_notification_service import notify_teachers_publisher_exhibition_registration
                user_role = session.get('role')
                if user_role == 'publisher':
                    notify_teachers_publisher_exhibition_registration(exhibition_id, user_id)
                elif user_role == 'dealer':
                    from app.services.exhibition_notification_service import notify_teachers_dealer_exhibition_registration
                    notify_teachers_dealer_exhibition_registration(exhibition_id, user_id)
            except Exception as e:
                # 记录错误但不影响主流程
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"发送参展通知邮件失败: {str(e)}")

            cursor.close()
            connection.close()

            return jsonify({"code": 0, "message": action_message})
        except Exception as e:
            connection.rollback()
            raise e

    except Exception as e:
        return jsonify({"code": 1, "message": f"报名失败: {str(e)}"})

@common_bp.route('/cancel_exhibition_registration', methods=['POST'])
def cancel_exhibition_registration():
    """
    取消书展报名
    请求数据:
        exhibition_id: 书展ID
    返回:
        取消结果
    """
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "用户未登录"})

    try:
        exhibition_id = request.form.get('exhibition_id')
        if not exhibition_id:
            return jsonify({"code": 1, "message": "未提供书展ID"})

        user_id = session.get('user_id')

        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 检查书展是否存在
        query = """
        SELECT id, status, start_time, end_time
        FROM book_exhibitions
        WHERE id = %s
        """
        cursor.execute(query, (exhibition_id,))
        exhibition = cursor.fetchone()

        if not exhibition:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "书展不存在"})

        # 检查书展是否已开始
        now = datetime.datetime.now()
        if exhibition['start_time'] and exhibition['start_time'] <= now:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "书展已开始，无法取消报名"})

        # 查询报名记录
        query = """
        SELECT id, status FROM exhibition_registrations
        WHERE exhibition_id = %s AND publisher_id = %s
        """
        cursor.execute(query, (exhibition_id, user_id))
        registration = cursor.fetchone()

        if not registration:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "未找到报名记录"})

        if registration['status'] == 'cancelled':
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "报名已取消"})

        # 更新报名状态为已取消
        try:
            connection.begin()

            query = """
            UPDATE exhibition_registrations
            SET status = 'cancelled', updated_at = NOW()
            WHERE id = %s
            """
            cursor.execute(query, (registration['id'],))

            connection.commit()

            # 发送邮件通知教师
            try:
                from app.services.exhibition_notification_service import notify_teachers_publisher_exhibition_cancellation
                user_role = session.get('role')
                if user_role == 'publisher':
                    notify_teachers_publisher_exhibition_cancellation(exhibition_id, user_id)
                elif user_role == 'dealer':
                    from app.services.exhibition_notification_service import notify_teachers_dealer_exhibition_cancellation
                    notify_teachers_dealer_exhibition_cancellation(exhibition_id, user_id)
            except Exception as e:
                # 记录错误但不影响主流程
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"发送取消参展通知邮件失败: {str(e)}")

            cursor.close()
            connection.close()

            return jsonify({"code": 0, "message": "取消报名成功"})
        except Exception as e:
            connection.rollback()
            raise e

    except Exception as e:
        return jsonify({"code": 1, "message": f"取消报名失败: {str(e)}"})

@common_bp.route('/get_pending_exhibitions', methods=['GET'])
def get_pending_exhibitions():
    """
    获取协办方书展列表（支持状态筛选）
    请求参数:
        page: 页码，默认1
        limit: 每页数量，默认10
        search: 搜索关键词
        status: 状态筛选(pending_review, approved, rejected, all)，默认pending_review
    返回:
        书展列表和状态统计
    """
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "用户未登录"})

    try:
        user_id = session.get('user_id')
        user_role = session.get('role')

        # 只有出版社和经销商可以审核
        if user_role not in ['publisher', 'dealer']:
            return jsonify({"code": 1, "message": "无权限访问"})

        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 10))
        offset = (page - 1) * limit
        search = request.args.get('search', '').strip()
        status_filter = request.args.get('status', 'pending_review').strip()

        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 获取用户所属组织ID
        if user_role == 'publisher':
            cursor.execute("SELECT publisher_company_id FROM users WHERE user_id = %s", (user_id,))
            user_info = cursor.fetchone()
            company_id = user_info['publisher_company_id'] if user_info else None
            co_organizer_type = 'publisher'
        else:  # dealer
            cursor.execute("SELECT dealer_company_id FROM users WHERE user_id = %s", (user_id,))
            user_info = cursor.fetchone()
            company_id = user_info['dealer_company_id'] if user_info else None
            co_organizer_type = 'dealer'

        if not company_id:
            return jsonify({"code": 1, "message": "用户信息不完整"})

        # 构建查询条件
        where_clauses = [
            "be.co_organizer_type = %s",
            "be.co_organizer_id = %s"
        ]
        params = [co_organizer_type, company_id]

        # 状态筛选
        if status_filter == 'pending_review':
            where_clauses.append("be.status = 'pending_review'")
        elif status_filter == 'approved':
            where_clauses.append("be.status = 'published'")
        elif status_filter == 'rejected':
            where_clauses.append("be.status = 'rejected'")
        elif status_filter == 'all':
            where_clauses.append("be.status IN ('pending_review', 'published', 'rejected')")
        else:
            # 默认只显示待审核
            where_clauses.append("be.status = 'pending_review'")

        # 搜索条件
        if search:
            where_clauses.append("be.title LIKE %s")
            params.append(f"%{search}%")

        where_sql = " AND ".join(where_clauses)

        # 查询待审核书展列表
        query = f"""
        SELECT be.id, be.title, be.logo_url, be.description, be.start_time, be.end_time,
               be.registration_deadline, be.location, be.status, be.created_at,
               s.name as school_name, ei.name as contact_name, ei.phone as contact_phone,
               u.name as initiator_name
        FROM book_exhibitions be
        JOIN schools s ON be.school_id = s.id
        JOIN exhibition_initiators ei ON be.id = ei.exhibition_id
        JOIN users u ON be.initiator_id = u.user_id
        WHERE {where_sql}
        ORDER BY be.created_at DESC
        LIMIT %s OFFSET %s
        """

        params.extend([limit, offset])
        cursor.execute(query, params)
        exhibitions = cursor.fetchall()

        # 格式化时间
        for exhibition in exhibitions:
            exhibition['start_time'] = exhibition['start_time'].strftime('%Y-%m-%d %H:%M') if exhibition['start_time'] else ''
            exhibition['end_time'] = exhibition['end_time'].strftime('%Y-%m-%d %H:%M') if exhibition['end_time'] else ''
            exhibition['registration_deadline'] = exhibition['registration_deadline'].strftime('%Y-%m-%d %H:%M') if exhibition['registration_deadline'] else ''
            exhibition['created_at'] = exhibition['created_at'].strftime('%Y-%m-%d %H:%M') if exhibition['created_at'] else ''

        # 查询总数
        count_params = params[:-2]  # 去掉 LIMIT 相关参数
        count_query = f"""
        SELECT COUNT(be.id) as total
        FROM book_exhibitions be
        JOIN schools s ON be.school_id = s.id
        JOIN exhibition_initiators ei ON be.id = ei.exhibition_id
        JOIN users u ON be.initiator_id = u.user_id
        WHERE {where_sql}
        """
        cursor.execute(count_query, count_params)
        total = cursor.fetchone()['total']

        # 查询状态统计
        status_counts = {}
        base_where = f"be.co_organizer_type = '{co_organizer_type}' AND be.co_organizer_id = {company_id}"

        # 待审核数量
        cursor.execute(f"""
            SELECT COUNT(be.id) as count
            FROM book_exhibitions be
            WHERE {base_where} AND be.status = 'pending_review'
        """)
        status_counts['pending_review'] = cursor.fetchone()['count']

        # 已通过数量
        cursor.execute(f"""
            SELECT COUNT(be.id) as count
            FROM book_exhibitions be
            WHERE {base_where} AND be.status = 'published'
        """)
        status_counts['approved'] = cursor.fetchone()['count']

        # 已拒绝数量
        cursor.execute(f"""
            SELECT COUNT(be.id) as count
            FROM book_exhibitions be
            WHERE {base_where} AND be.status = 'rejected'
        """)
        status_counts['rejected'] = cursor.fetchone()['count']

        # 全部数量
        status_counts['all'] = status_counts['pending_review'] + status_counts['approved'] + status_counts['rejected']

        cursor.close()
        connection.close()

        return jsonify({
            "code": 0,
            "message": "获取成功",
            "data": {
                "exhibitions": exhibitions,
                "total": total,
                "page": page,
                "limit": limit,
                "status_counts": status_counts
            }
        })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取待审核书展失败: {str(e)}"})

@common_bp.route('/get_registration_detail', methods=['GET'])
def get_registration_detail():
    """
    获取书展报名详情
    请求参数:
        exhibition_id: 书展ID
    返回:
        报名信息、参展人员
    """
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "用户未登录"})

    try:
        exhibition_id = request.args.get('exhibition_id')
        if not exhibition_id:
            return jsonify({"code": 1, "message": "未提供书展ID"})

        user_id = session.get('user_id')

        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 查询报名信息（只查询已报名状态的记录）
        query = """
        SELECT id, exhibition_id, status, created_at, updated_at
        FROM exhibition_registrations
        WHERE exhibition_id = %s AND publisher_id = %s AND status = 'registered'
        """
        cursor.execute(query, (exhibition_id, user_id))
        registration = cursor.fetchone()

        if not registration:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "未找到报名信息"})

        # 格式化时间
        registration['created_at'] = registration['created_at'].strftime('%Y-%m-%d %H:%M:%S') if registration['created_at'] else ''
        registration['updated_at'] = registration['updated_at'].strftime('%Y-%m-%d %H:%M:%S') if registration['updated_at'] else ''

        # 查询参展人员
        query = """
        SELECT id, name, phone, role, license_plate, is_contact
        FROM exhibition_participants
        WHERE registration_id = %s
        ORDER BY is_contact DESC, id ASC
        """
        cursor.execute(query, (registration['id'],))
        participants = cursor.fetchall()

        cursor.close()
        connection.close()

        return jsonify({
            "code": 0,
            "message": "获取成功",
            "data": {
                "registration": registration,
                "participants": participants
            }
        })
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取报名详情失败: {str(e)}"})

@common_bp.route('/review_exhibition', methods=['POST'])
def review_exhibition():
    """
    审核书展活动（协办方使用）
    请求数据:
        exhibition_id: 书展ID
        action: 审核动作 ('approve' 或 'reject')
        comment: 审核意见（可选）
        contact_ids: 联系人ID列表（审核通过时必填）
    返回:
        审核结果
    """
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "用户未登录"})

    try:
        data = request.get_json()
        if not data:
            return jsonify({"code": 1, "message": "请求数据为空"})

        exhibition_id = data.get('exhibition_id')
        action = data.get('action')  # 'approve' 或 'reject'
        comment = data.get('comment', '').strip()
        contact_ids = data.get('contact_ids', [])  # 联系人ID列表
        contact_privacy = data.get('contact_privacy', [])  # 联系人保密设置列表

        if not exhibition_id:
            return jsonify({"code": 1, "message": "未提供书展ID"})
        if action not in ['approve', 'reject']:
            return jsonify({"code": 1, "message": "无效的审核动作"})

        # 审核通过时必须选择联系人
        if action == 'approve' and not contact_ids:
            return jsonify({"code": 1, "message": "审核通过时必须选择联系人"})

        user_id = session.get('user_id')
        user_role = session.get('role')

        # 只有出版社和经销商可以审核
        if user_role not in ['publisher', 'dealer']:
            return jsonify({"code": 1, "message": "无权限审核"})

        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 获取用户所属组织ID
        if user_role == 'publisher':
            cursor.execute("SELECT publisher_company_id FROM users WHERE user_id = %s", (user_id,))
            user_info = cursor.fetchone()
            company_id = user_info['publisher_company_id'] if user_info else None
            reviewer_type = 'publisher'
        else:  # dealer
            cursor.execute("SELECT dealer_company_id FROM users WHERE user_id = %s", (user_id,))
            user_info = cursor.fetchone()
            company_id = user_info['dealer_company_id'] if user_info else None
            reviewer_type = 'dealer'

        if not company_id:
            return jsonify({"code": 1, "message": "用户信息不完整"})

        # 验证是否有权限审核此书展
        cursor.execute("""
            SELECT id, status, co_organizer_type, co_organizer_id, title
            FROM book_exhibitions
            WHERE id = %s
        """, (exhibition_id,))
        exhibition = cursor.fetchone()

        if not exhibition:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "书展不存在"})

        if exhibition['status'] != 'pending_review':
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "书展不在待审核状态"})

        if (exhibition['co_organizer_type'] != reviewer_type or
            exhibition['co_organizer_id'] != company_id):
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "无权限审核此书展"})

        # 执行审核操作
        try:
            connection.begin()

            # 更新书展状态
            new_status = 'published' if action == 'approve' else 'rejected'
            cursor.execute("""
                UPDATE book_exhibitions
                SET status = %s, updated_at = NOW()
                WHERE id = %s
            """, (new_status, exhibition_id))

            # 如果审核通过，保存联系人关联关系
            if action == 'approve' and contact_ids:
                # 先删除已有的联系人关联（防止重复审核）
                cursor.execute("""
                    DELETE FROM exhibition_contacts
                    WHERE exhibition_id = %s
                """, (exhibition_id,))

                # 验证联系人是否属于当前协办方
                placeholders = ', '.join(['%s'] * len(contact_ids))
                cursor.execute(f"""
                    SELECT id FROM co_organizer_contacts
                    WHERE id IN ({placeholders})
                    AND co_organizer_type = %s AND co_organizer_id = %s
                    AND is_active = 1
                """, contact_ids + [reviewer_type, company_id])

                valid_contacts = cursor.fetchall()
                valid_contact_ids = [contact['id'] for contact in valid_contacts]

                if len(valid_contact_ids) != len(contact_ids):
                    connection.rollback()
                    return jsonify({"code": 1, "message": "选择的联系人中包含无效联系人"})

                # 插入联系人关联关系
                for contact_id in valid_contact_ids:
                    cursor.execute("""
                        INSERT INTO exhibition_contacts (exhibition_id, contact_id)
                        VALUES (%s, %s)
                    """, (exhibition_id, contact_id))

                # 处理联系人保密设置
                if contact_privacy:
                    # 删除现有的协办方联系人保密设置
                    cursor.execute("""
                        DELETE FROM exhibition_contact_privacy
                        WHERE exhibition_id = %s AND contact_type = 'co_organizer'
                    """, (exhibition_id,))

                    # 添加新的保密设置
                    for privacy_setting in contact_privacy:
                        contact_id = privacy_setting.get('contact_id')
                        is_confidential = privacy_setting.get('is_confidential', False)

                        if contact_id and is_confidential:
                            cursor.execute("""
                                INSERT INTO exhibition_contact_privacy
                                (exhibition_id, contact_type, contact_id, is_confidential, created_by)
                                VALUES (%s, 'co_organizer', %s, 1, %s)
                            """, (exhibition_id, contact_id, user_id))

            # 记录审核历史
            cursor.execute("""
                INSERT INTO exhibition_review_history
                (exhibition_id, reviewer_id, reviewer_type, action, old_status, new_status, review_comment)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """, (
                exhibition_id, user_id, reviewer_type, action,
                'pending_review', new_status, comment
            ))

            connection.commit()

            # 发送邮件通知教师审核结果
            try:
                from app.services.exhibition_notification_service import notify_teacher_exhibition_review_result
                notify_teacher_exhibition_review_result(exhibition_id, action, comment)
            except Exception as e:
                # 记录错误但不影响主流程
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"发送协办方审核结果通知邮件失败: {str(e)}")

            action_text = '审核通过' if action == 'approve' else '审核拒绝'
            return jsonify({
                "code": 0,
                "message": f"书展《{exhibition['title']}》{action_text}成功"
            })

        except Exception as e:
            connection.rollback()
            raise e
        finally:
            cursor.close()
            connection.close()

    except Exception as e:
        return jsonify({"code": 1, "message": f"审核失败: {str(e)}"})

@common_bp.route('/get_exhibition_review_history', methods=['GET'])
def get_exhibition_review_history():
    """
    获取书展审核历史
    请求参数:
        exhibition_id: 书展ID
    返回:
        审核历史记录
    """
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "用户未登录"})

    try:
        exhibition_id = request.args.get('exhibition_id')
        if not exhibition_id:
            return jsonify({"code": 1, "message": "未提供书展ID"})

        user_id = session.get('user_id')
        user_role = session.get('role')

        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 验证权限：发起人、协办方可查看
        cursor.execute("""
            SELECT initiator_id, co_organizer_type, co_organizer_id
            FROM book_exhibitions
            WHERE id = %s
        """, (exhibition_id,))
        exhibition = cursor.fetchone()

        if not exhibition:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "书展不存在"})

        # 检查权限
        has_permission = False

        # 发起人可查看
        if exhibition['initiator_id'] == user_id:
            has_permission = True

        # 协办方可查看
        if not has_permission and exhibition['co_organizer_type'] and exhibition['co_organizer_id']:
            if user_role == 'publisher' and exhibition['co_organizer_type'] == 'publisher':
                cursor.execute("SELECT publisher_company_id FROM users WHERE user_id = %s", (user_id,))
                user_info = cursor.fetchone()
                if user_info and user_info['publisher_company_id'] == exhibition['co_organizer_id']:
                    has_permission = True
            elif user_role == 'dealer' and exhibition['co_organizer_type'] == 'dealer':
                cursor.execute("SELECT dealer_company_id FROM users WHERE user_id = %s", (user_id,))
                user_info = cursor.fetchone()
                if user_info and user_info['dealer_company_id'] == exhibition['co_organizer_id']:
                    has_permission = True

        if not has_permission:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "无权限查看审核历史"})

        # 查询审核历史
        cursor.execute("""
            SELECT erh.*, u.name as reviewer_name,
                   CASE
                       WHEN erh.reviewer_type = 'teacher' THEN s.name
                       WHEN erh.reviewer_type = 'publisher' THEN pc.name
                       WHEN erh.reviewer_type = 'dealer' THEN dc.name
                       ELSE '未知'
                   END as reviewer_company
            FROM exhibition_review_history erh
            JOIN users u ON erh.reviewer_id = u.user_id
            LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id AND erh.reviewer_type = 'publisher'
            LEFT JOIN dealer_companies dc ON u.dealer_company_id = dc.id AND erh.reviewer_type = 'dealer'
            LEFT JOIN schools s ON u.teacher_school_id = s.id AND erh.reviewer_type = 'teacher'
            WHERE erh.exhibition_id = %s
            ORDER BY erh.created_at DESC
        """, (exhibition_id,))
        history = cursor.fetchall()

        # 格式化时间
        for record in history:
            record['created_at'] = record['created_at'].strftime('%Y-%m-%d %H:%M:%S') if record['created_at'] else ''

        cursor.close()
        connection.close()

        return jsonify({
            "code": 0,
            "message": "获取成功",
            "data": history
        })

    except Exception as e:
        return jsonify({"code": 1, "message": f"获取审核历史失败: {str(e)}"})

@common_bp.route('/get_co_organizer_contacts', methods=['GET'])
def get_co_organizer_contacts():
    """
    获取协办方联系人列表
    请求参数:
        search: 搜索关键词（可选）
    返回:
        联系人列表
    """
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "用户未登录"})

    user_id = session.get('user_id')
    user_role = session.get('role')
    search = request.args.get('search', '').strip()

    # 只有出版社和经销商可以访问
    if user_role not in ['publisher', 'dealer']:
        return jsonify({"code": 1, "message": "无权限访问"})

    try:
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 获取协办方信息
        if user_role == 'publisher':
            cursor.execute("SELECT publisher_company_id as company_id FROM users WHERE user_id = %s", (user_id,))
            reviewer_type = 'publisher'
        else:  # dealer
            cursor.execute("SELECT dealer_company_id as company_id FROM users WHERE user_id = %s", (user_id,))
            reviewer_type = 'dealer'

        user_info = cursor.fetchone()
        if not user_info or not user_info['company_id']:
            return jsonify({"code": 1, "message": "用户公司信息不完整"})

        company_id = user_info['company_id']

        # 构建查询SQL
        sql = """
            SELECT id, name, phone, company_name, created_at
            FROM co_organizer_contacts
            WHERE co_organizer_type = %s AND co_organizer_id = %s AND is_active = 1
        """
        params = [reviewer_type, company_id]

        if search:
            sql += " AND name LIKE %s"
            params.append(f"%{search}%")

        sql += " ORDER BY created_at DESC"

        cursor.execute(sql, params)
        contacts = cursor.fetchall()

        # 格式化时间
        for contact in contacts:
            if contact.get('created_at'):
                contact['created_at'] = contact['created_at'].strftime('%Y-%m-%d %H:%M:%S')

        cursor.close()
        connection.close()

        return jsonify({
            "code": 0,
            "message": "获取成功",
            "data": contacts
        })

    except Exception as e:
        return jsonify({"code": 1, "message": f"获取联系人失败: {str(e)}"})

@common_bp.route('/add_co_organizer_contact', methods=['POST'])
def add_co_organizer_contact():
    """
    添加协办方联系人
    请求数据:
        name: 姓名
        phone: 手机号
        company_name: 所属单位
        position: 职务（可选）
        email: 邮箱（可选）
    返回:
        添加结果
    """
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "用户未登录"})

    user_id = session.get('user_id')
    user_role = session.get('role')

    # 只有出版社和经销商可以添加
    if user_role not in ['publisher', 'dealer']:
        return jsonify({"code": 1, "message": "无权限添加"})

    try:
        data = request.get_json()
        if not data:
            return jsonify({"code": 1, "message": "请求数据为空"})

        name = data.get('name', '').strip()
        phone = data.get('phone', '').strip()
        company_name = data.get('company_name', '').strip()

        if not all([name, phone, company_name]):
            return jsonify({"code": 1, "message": "姓名、手机号和所属单位为必填项"})

        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 获取协办方信息
        if user_role == 'publisher':
            cursor.execute("SELECT publisher_company_id as company_id FROM users WHERE user_id = %s", (user_id,))
            reviewer_type = 'publisher'
        else:  # dealer
            cursor.execute("SELECT dealer_company_id as company_id FROM users WHERE user_id = %s", (user_id,))
            reviewer_type = 'dealer'

        user_info = cursor.fetchone()
        if not user_info or not user_info['company_id']:
            return jsonify({"code": 1, "message": "用户公司信息不完整"})

        company_id = user_info['company_id']

        # 检查手机号是否已存在
        cursor.execute("""
            SELECT id FROM co_organizer_contacts
            WHERE co_organizer_type = %s AND co_organizer_id = %s AND phone = %s AND is_active = 1
        """, (reviewer_type, company_id, phone))

        if cursor.fetchone():
            return jsonify({"code": 1, "message": "该手机号已存在"})

        # 插入联系人
        cursor.execute("""
            INSERT INTO co_organizer_contacts
            (co_organizer_type, co_organizer_id, name, phone, company_name)
            VALUES (%s, %s, %s, %s, %s)
        """, (reviewer_type, company_id, name, phone, company_name))

        connection.commit()
        new_contact_id = cursor.lastrowid

        cursor.close()
        connection.close()

        return jsonify({
            "code": 0,
            "message": "联系人添加成功",
            "data": {"id": new_contact_id}
        })

    except Exception as e:
        return jsonify({"code": 1, "message": f"添加联系人失败: {str(e)}"})

@common_bp.route('/edit_co_organizer_contact', methods=['POST'])
def edit_co_organizer_contact():
    """
    编辑协办方联系人
    请求数据:
        id: 联系人ID
        name: 姓名
        phone: 手机号
        company_name: 所属单位
        position: 职务（可选）
        email: 邮箱（可选）
    返回:
        编辑结果
    """
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "用户未登录"})

    user_id = session.get('user_id')
    user_role = session.get('role')

    # 只有出版社和经销商可以编辑
    if user_role not in ['publisher', 'dealer']:
        return jsonify({"code": 1, "message": "无权限编辑"})

    try:
        data = request.get_json()
        if not data:
            return jsonify({"code": 1, "message": "请求数据为空"})

        contact_id = data.get('id')
        name = data.get('name', '').strip()
        phone = data.get('phone', '').strip()
        company_name = data.get('company_name', '').strip()

        if not contact_id:
            return jsonify({"code": 1, "message": "联系人ID不能为空"})
        if not all([name, phone, company_name]):
            return jsonify({"code": 1, "message": "姓名、手机号和所属单位为必填项"})

        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 获取协办方信息
        if user_role == 'publisher':
            cursor.execute("SELECT publisher_company_id as company_id FROM users WHERE user_id = %s", (user_id,))
            reviewer_type = 'publisher'
        else:  # dealer
            cursor.execute("SELECT dealer_company_id as company_id FROM users WHERE user_id = %s", (user_id,))
            reviewer_type = 'dealer'

        user_info = cursor.fetchone()
        if not user_info or not user_info['company_id']:
            return jsonify({"code": 1, "message": "用户公司信息不完整"})

        company_id = user_info['company_id']

        # 检查联系人是否存在且属于当前协办方
        cursor.execute("""
            SELECT id FROM co_organizer_contacts
            WHERE id = %s AND co_organizer_type = %s AND co_organizer_id = %s AND is_active = 1
        """, (contact_id, reviewer_type, company_id))

        if not cursor.fetchone():
            return jsonify({"code": 1, "message": "联系人不存在或无权限编辑"})

        # 检查手机号是否被其他联系人使用
        cursor.execute("""
            SELECT id FROM co_organizer_contacts
            WHERE co_organizer_type = %s AND co_organizer_id = %s AND phone = %s
            AND is_active = 1 AND id != %s
        """, (reviewer_type, company_id, phone, contact_id))

        if cursor.fetchone():
            return jsonify({"code": 1, "message": "该手机号已被其他联系人使用"})

        # 更新联系人信息
        cursor.execute("""
            UPDATE co_organizer_contacts
            SET name = %s, phone = %s, company_name = %s, updated_at = NOW()
            WHERE id = %s
        """, (name, phone, company_name, contact_id))

        connection.commit()
        cursor.close()
        connection.close()

        return jsonify({
            "code": 0,
            "message": "联系人更新成功"
        })

    except Exception as e:
        return jsonify({"code": 1, "message": f"编辑联系人失败: {str(e)}"})

@common_bp.route('/delete_co_organizer_contact', methods=['POST'])
def delete_co_organizer_contact():
    """
    删除协办方联系人
    请求数据:
        id: 联系人ID
    返回:
        删除结果
    """
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "用户未登录"})

    user_id = session.get('user_id')
    user_role = session.get('role')

    # 只有出版社和经销商可以删除
    if user_role not in ['publisher', 'dealer']:
        return jsonify({"code": 1, "message": "无权限删除"})

    try:
        data = request.get_json()
        if not data:
            return jsonify({"code": 1, "message": "请求数据为空"})

        contact_id = data.get('id')
        if not contact_id:
            return jsonify({"code": 1, "message": "联系人ID不能为空"})

        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 获取协办方信息
        if user_role == 'publisher':
            cursor.execute("SELECT publisher_company_id as company_id FROM users WHERE user_id = %s", (user_id,))
            reviewer_type = 'publisher'
        else:  # dealer
            cursor.execute("SELECT dealer_company_id as company_id FROM users WHERE user_id = %s", (user_id,))
            reviewer_type = 'dealer'

        user_info = cursor.fetchone()
        if not user_info or not user_info['company_id']:
            return jsonify({"code": 1, "message": "用户公司信息不完整"})

        company_id = user_info['company_id']

        # 检查联系人是否存在且属于当前协办方
        cursor.execute("""
            SELECT id FROM co_organizer_contacts
            WHERE id = %s AND co_organizer_type = %s AND co_organizer_id = %s AND is_active = 1
        """, (contact_id, reviewer_type, company_id))

        if not cursor.fetchone():
            return jsonify({"code": 1, "message": "联系人不存在或无权限删除"})

        # 检查是否有书展正在使用此联系人
        cursor.execute("""
            SELECT COUNT(*) as count FROM exhibition_contacts ec
            JOIN book_exhibitions be ON ec.exhibition_id = be.id
            WHERE ec.contact_id = %s AND be.status IN ('published', 'pending_review')
        """, (contact_id,))

        result = cursor.fetchone()
        if result and result['count'] > 0:
            return jsonify({"code": 1, "message": "该联系人正在被书展使用，无法删除"})

        # 软删除联系人
        cursor.execute("""
            UPDATE co_organizer_contacts
            SET is_active = 0, updated_at = NOW()
            WHERE id = %s
        """, (contact_id,))

        connection.commit()
        cursor.close()
        connection.close()

        return jsonify({
            "code": 0,
            "message": "联系人删除成功"
        })

    except Exception as e:
        return jsonify({"code": 1, "message": f"删除联系人失败: {str(e)}"})

@common_bp.route('/get_exhibition_contacts', methods=['GET'])
def get_exhibition_contacts():
    """
    获取书展联系人信息
    请求参数:
        exhibition_id: 书展ID
    返回:
        联系人列表
    """
    exhibition_id = request.args.get('exhibition_id')
    if not exhibition_id:
        return jsonify({"code": 1, "message": "书展ID不能为空"})

    try:
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 查询书展联系人信息
        cursor.execute("""
            SELECT c.id, c.name, c.phone, c.company_name
            FROM exhibition_contacts ec
            JOIN co_organizer_contacts c ON ec.contact_id = c.id
            WHERE ec.exhibition_id = %s AND c.is_active = 1
            ORDER BY c.name
        """, (exhibition_id,))

        contacts = cursor.fetchall()

        cursor.close()
        connection.close()

        return jsonify({
            "code": 0,
            "message": "获取成功",
            "data": contacts
        })

    except Exception as e:
        return jsonify({"code": 1, "message": f"获取书展联系人失败: {str(e)}"})

@common_bp.route('/cancel_registration', methods=['POST'])
def cancel_registration():
    """
    取消书展报名
    请求数据:
        exhibition_id: 书展ID
    返回:
        取消结果
    """
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "用户未登录"})

    try:
        data = request.get_json()
        if not data:
            return jsonify({"code": 1, "message": "请求数据为空"})

        exhibition_id = data.get('exhibition_id')
        if not exhibition_id:
            return jsonify({"code": 1, "message": "未提供书展ID"})

        user_id = session.get('user_id')

        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 查询报名信息
        query = """
        SELECT id, exhibition_id, status
        FROM exhibition_registrations
        WHERE exhibition_id = %s AND publisher_id = %s
        """
        cursor.execute(query, (exhibition_id, user_id))
        registration = cursor.fetchone()

        if not registration:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "未找到报名信息"})

        # 检查书展状态
        query = """
        SELECT status, start_time
        FROM book_exhibitions
        WHERE id = %s
        """
        cursor.execute(query, (exhibition_id,))
        exhibition = cursor.fetchone()

        if not exhibition:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "书展不存在"})

        # 检查书展是否已开始
        if exhibition['start_time'] and exhibition['start_time'] <= datetime.datetime.now():
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "书展已开始，无法取消报名"})

        # 更新报名状态为已取消
        try:
            connection.begin()

            query = """
            UPDATE exhibition_registrations
            SET status = 'cancelled', updated_at = NOW()
            WHERE id = %s
            """
            cursor.execute(query, (registration['id'],))

            connection.commit()

            # 发送邮件通知教师
            try:
                from app.services.exhibition_notification_service import notify_teachers_publisher_exhibition_cancellation
                user_role = session.get('role')
                if user_role == 'publisher':
                    notify_teachers_publisher_exhibition_cancellation(exhibition_id, user_id)
                elif user_role == 'dealer':
                    from app.services.exhibition_notification_service import notify_teachers_dealer_exhibition_cancellation
                    notify_teachers_dealer_exhibition_cancellation(exhibition_id, user_id)
            except Exception as e:
                # 记录错误但不影响主流程
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"发送取消参展通知邮件失败: {str(e)}")

            cursor.close()
            connection.close()

            return jsonify({"code": 0, "message": "取消报名成功"})
        except Exception as e:
            connection.rollback()
            raise e
    except Exception as e:
        return jsonify({"code": 1, "message": f"取消报名失败: {str(e)}"})


@common_bp.route('/register', methods=['POST'])
def register():
    """
    用户注册接口，仅限教师用户注册
    请求数据:
        username: 用户名
        password: 密码
        name: 姓名
        phone_number: 手机号
        role: 角色 (只接受'teacher')
        school_id: 学校ID
        email: 邮箱
    返回:
        注册结果
    """
    import re  # 移到函数开头

    try:
        # 从请求中获取数据
        data = request.get_json() if request.is_json else request.form.to_dict()

        username = data.get('username')
        password = data.get('password')
        name = data.get('name')
        phone_number = data.get('phone_number')
        role = data.get('role')
        school_id = data.get('school_id')
        email = data.get('email')
        verification_code = data.get('verification_code')  # 新增验证码字段

        # 检查必填字段
        if not all([username, password, name, phone_number, role, school_id, email, verification_code]):
            return jsonify({"status": "fail", "message": "所有字段都是必填项，请填写验证码"})

        # 验证角色，仅允许教师注册
        if role != 'teacher':
            return jsonify({"status": "fail", "message": "只允许教师用户注册，其他角色请联系管理员"})

        # 验证用户名格式（不能小于3个字符）
        if len(username.strip()) < 3:
            return jsonify({"status": "fail", "message": "用户名不能少于3个字符"})

        # 验证用户名格式（只允许字母、数字、下划线）
        if not re.match(r'^[a-zA-Z0-9_]+$', username):
            return jsonify({"status": "fail", "message": "用户名只能包含字母、数字和下划线"})

        # 验证手机号格式
        if not re.match(r'^1[3-9]\d{9}$', phone_number):
            return jsonify({"status": "fail", "message": "请输入正确的手机号码"})

        # 验证邮箱格式
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            return jsonify({"status": "fail", "message": "请输入正确的邮箱地址"})

        # 验证邮箱验证码
        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 查询验证码是否有效
            code_sql = """
            SELECT id, code, expires_at, used
            FROM registration_verification_codes
            WHERE email = %s
            ORDER BY created_at DESC LIMIT 1
            """
            cursor.execute(code_sql, (email,))
            code_record = cursor.fetchone()

            if not code_record:
                return jsonify({"status": "fail", "message": "验证码不存在，请先获取验证码"})

            # 验证码是否已使用
            if code_record['used']:
                return jsonify({"status": "fail", "message": "验证码已使用，请重新获取"})

            # 验证码过期检查
            if datetime.datetime.now() > code_record['expires_at']:
                return jsonify({"status": "fail", "message": "验证码已过期，请重新获取"})

            # 验证码是否正确
            if verification_code != code_record['code']:
                return jsonify({"status": "fail", "message": "验证码不正确"})

        # 验证密码强度
        # if len(password) < 8:
        #     return jsonify({"status": "fail", "message": "密码长度至少为8位"})

        # if not (re.search(r'[A-Z]', password) and re.search(r'[a-z]', password)
        #         and re.search(r'\d', password) and re.search(r'[!@#$%^&*()_+\-=\[\]{};:\'",<>\./?\\|]', password)):
        #     return jsonify({"status": "fail", "message": "密码必须包含大小写字母、数字和特殊字符"})

        # 检查用户名是否已存在
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        cursor.execute("SELECT user_id FROM users WHERE username = %s", (username,))
        if cursor.fetchone():
            cursor.close()
            connection.close()
            return jsonify({"status": "fail", "message": "用户名已存在，请使用其他用户名"})

        # 检查手机号是否已存在
        cursor.execute("SELECT user_id FROM users WHERE phone_number = %s", (phone_number,))
        if cursor.fetchone():
            cursor.close()
            connection.close()
            return jsonify({"status": "fail", "message": "手机号已注册，请使用其他手机号或找回账号"})

        # 检查邮箱是否已存在
        cursor.execute("SELECT user_id FROM users WHERE email = %s", (email,))
        if cursor.fetchone():
            cursor.close()
            connection.close()
            return jsonify({"status": "fail", "message": "邮箱已注册，请使用其他邮箱或找回账号"})

        # 检查学校是否存在
        cursor.execute("SELECT id FROM schools WHERE id = %s", (school_id,))
        if not cursor.fetchone():
            cursor.close()
            connection.close()
            return jsonify({"status": "fail", "message": "选择的学校不存在"})

        # 生成哈希密码
        hashed_password = generate_password_hash(password)

        # 创建用户
        try:
            cursor.execute("""
            INSERT INTO users (username, password, name, phone_number, email, role, teacher_school_id, created_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, NOW())
            """, (username, hashed_password, name, phone_number, email, role, school_id))

            user_id = cursor.lastrowid

            # 标记验证码为已使用
            update_code_sql = """
            UPDATE registration_verification_codes
            SET used = 1, used_at = NOW()
            WHERE email = %s AND code = %s
            """
            cursor.execute(update_code_sql, (email, verification_code))

            # 处理邀请码（如果提供了邀请码）
            invitation_code = data.get('invitation_code', '').strip()
            if invitation_code:
                try:
                    print(f"检测到邀请码: {invitation_code}")
                    # 查询邀请码信息
                    cursor.execute("""
                        SELECT ic.id, ic.inviter_id, u.name as inviter_name,
                               dc.name as company_name, dcp.can_invite_users
                        FROM invitation_codes ic
                        JOIN users u ON ic.inviter_id = u.user_id
                        LEFT JOIN dealer_companies dc ON u.dealer_company_id = dc.id
                        LEFT JOIN dealer_company_permissions dcp ON dc.id = dcp.company_id
                        WHERE ic.code = %s AND u.role = 'dealer'
                    """, (invitation_code,))
                    invitation = cursor.fetchone()

                    if invitation and invitation.get('can_invite_users'):
                        inviter_id = invitation['inviter_id']

                        # 记录注册来源到user_registrations表
                        cursor.execute("""
                            INSERT INTO user_registrations (user_id, source_type, source_id, source_token, registered_at)
                            VALUES (%s, 'invitation', %s, %s, NOW())
                        """, (user_id, inviter_id, invitation_code))

                        print(f"成功记录邀请码使用: 用户{user_id}通过邀请码{invitation_code}注册")
                    else:
                        print(f"邀请码{invitation_code}无效或邀请人没有权限")
                except Exception as e:
                    # 邀请码处理失败不影响注册流程
                    print(f"处理邀请码失败: {str(e)}")

            # 记录注册来源（如果是通过分享清单链接注册的）
            # 优先从return_to参数中获取来源信息，其次从referer中获取
            source_url = None
            referer = request.headers.get('Referer', '')

            # 检查是否有return_to参数（从URL参数中获取）
            if 'return_to' in data:
                source_url = data['return_to']
                print(f"从return_to参数获取来源: {source_url}")
            elif referer:
                source_url = referer
                print(f"从referer获取来源: {source_url}")

            if source_url and ('/public/shared-lists/' in source_url or '/shared/' in source_url):
                try:
                    print(f"检测到分享清单来源: {source_url}")
                    # 从URL中提取分享token
                    # 支持两种URL格式：/public/shared-lists/{token} 和 /shared/{token}
                    token_match = re.search(r'/(?:public/shared-lists|shared)/([a-f0-9-]{36})', source_url)
                    if token_match:
                        share_token = token_match.group(1)
                        print(f"提取到分享token: {share_token}")

                        # 查询清单ID
                        cursor.execute("SELECT id FROM shared_lists WHERE share_token = %s", (share_token,))
                        list_result = cursor.fetchone()

                        if list_result:
                            list_id = list_result['id']
                            print(f"找到清单ID: {list_id}")

                            # 检查是否已经记录过该用户的注册来源
                            cursor.execute("""
                                SELECT id FROM user_registrations
                                WHERE user_id = %s AND source_type = 'shared_list' AND source_id = %s
                            """, (user_id, list_id))
                            existing_record = cursor.fetchone()

                            if not existing_record:
                                cursor.execute("""
                                    INSERT INTO user_registrations (user_id, source_type, source_id, source_token, source_url, registered_at)
                                    VALUES (%s, 'shared_list', %s, %s, %s, NOW())
                                """, (user_id, list_id, share_token, source_url))
                                print(f"成功记录注册来源: 用户{user_id}通过清单{list_id}注册")
                            else:
                                print(f"用户{user_id}的清单{list_id}注册来源已存在，跳过记录")
                        else:
                            print(f"未找到对应的清单: {share_token}")
                    else:
                        print(f"无法从URL中提取token: {source_url}")
                except Exception as e:
                    # 记录来源失败不影响注册流程
                    print(f"记录注册来源失败: {str(e)}")
            else:
                print(f"未检测到分享清单来源，source_url: {source_url}")

            connection.commit()

            # 记录注册成功日志
            AuditLogService.log_action(
                action_type=AuditLogService.ActionType.REGISTER,
                description=f"新用户注册：{username}",
                target_type='user',
                target_id=user_id,
                details={
                    'username': username,
                    'role': role,
                    'name': name,
                    'school_id': school_id,
                    'registration_method': 'web',
                    'has_source_url': bool(source_url)
                },
                user_id=user_id
            )

            cursor.close()
            connection.close()

            # 发送注册成功邮件
            try:
                from app.services.site_config_service import get_site_name
                site_name = get_site_name()
                welcome_subject = f"【{site_name}】欢迎您的加入！"
                welcome_content = f"""尊敬的{name}老师，

欢迎您注册{site_name}！

您的账号信息：
• 用户名：{username}
• 姓名：{name}
• 邮箱：{email}
• 注册时间：{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

您现在可以使用以下功能：
• 浏览和申请样书
• 创建和管理书展活动
• 申请课件资源
• 管理个人信息

如有任何问题，请联系系统管理员。

此致
敬礼！

{site_name}
{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""

                from app.services.email_service import send_success_email
                send_success_email(email, welcome_subject, welcome_content)
            except Exception as e:
                # 记录错误但不影响注册流程
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"发送注册成功邮件失败: {str(e)}")

            return jsonify({"status": "success", "message": "注册成功", "user_id": user_id})

        except Exception as e:
            connection.rollback()
            raise e

    except Exception as e:
        # 记录注册失败日志
        AuditLogService.log_action(
            action_type=AuditLogService.ActionType.REGISTER,
            result=AuditLogService.Result.FAILURE,
            description="用户注册失败",
            details={
                'attempted_username': data.get('username', '') if 'data' in locals() else '',
                'error_reason': str(e),
                'registration_method': 'web'
            }
        )
        return jsonify({"status": "fail", "message": f"注册失败: {str(e)}"})

@common_bp.route('/get_nav_data', methods=['GET'])
def get_nav_data():
    """
    获取导航菜单数据和用户角色信息
    返回:
        用户角色和对应的导航菜单项
    """
    if 'user_id' not in session:
        return jsonify({"status": "error", "message": "用户未登录"})

    try:
        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        user_id = session.get('user_id')
        user_role = session.get('role')

        # 获取用户详细信息，包括名称
        query = """
        SELECT
            u.user_id,
            u.username,
            u.name,
            u.nickname,
            u.role,
            u.teacher_school_id,
            s.name as school_name,
            u.publisher_company_id,
            pc.name as publisher_company_name,
            u.dealer_company_id,
            dc.name as dealer_company_name
        FROM users u
        LEFT JOIN schools s ON u.teacher_school_id = s.id
        LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id
        LEFT JOIN dealer_companies dc ON u.dealer_company_id = dc.id
        WHERE u.user_id = %s
        """
        cursor.execute(query, (user_id,))
        user_info = cursor.fetchone()

        if not user_info:
            return jsonify({"status": "error", "message": "用户不存在"})

        # 根据角色设置显示名称
        display_name = user_info['username']  # 默认使用用户名
        if user_role == 'teacher' and user_info['name']:
            display_name = user_info['name']  # 教师显示姓名
        elif user_role == 'publisher' and user_info['publisher_company_name']:
            display_name = user_info['publisher_company_name']  # 出版社显示单位名称
        elif user_role == 'dealer' and user_info['dealer_company_name']:
            display_name = user_info['dealer_company_name']  # 经销商显示单位名称

        # 将显示名称添加到session
        session['name'] = display_name

        # 根据角色生成导航菜单
        nav_items = []
        initial_page = '/login'

        # 检查用户的权限
        user_permissions = {
            'can_register_exhibition': False,
            'can_initiate_exhibition': False,
            'can_recommend_books': False,
            'can_invite_users': False,
            'can_view_rate_info': False
        }

        # 根据用户角色查询权限
        if user_role == 'publisher':
            query = """
            SELECT pcp.can_register_exhibition, pcp.can_recommend_books
            FROM publisher_company_permissions pcp
            WHERE pcp.company_id = %s
            """
            cursor.execute(query, (user_info['publisher_company_id'],))
            permission = cursor.fetchone()
            if permission:
                user_permissions['can_register_exhibition'] = bool(permission['can_register_exhibition'])
                user_permissions['can_recommend_books'] = bool(permission['can_recommend_books'])

            # 检查细分的费率查看权限
            from app.users.publisher import check_user_permission
            user_permissions['can_view_shipping_discount'] = check_user_permission(user_id, 'view_shipping_discount')
            user_permissions['can_view_settlement_discount'] = check_user_permission(user_id, 'view_settlement_discount')
            user_permissions['can_view_promotion_rate'] = check_user_permission(user_id, 'view_promotion_rate')

            # 为了向后兼容，如果用户有三个费率权限，就认为有费率查看权限
            user_permissions['can_view_rate_info'] = (
                user_permissions['can_view_shipping_discount'] and
                user_permissions['can_view_settlement_discount'] and
                user_permissions['can_view_promotion_rate']
            )

        elif user_role == 'dealer':
            query = """
            SELECT dcp.can_register_exhibition, dcp.can_initiate_exhibition, dcp.can_invite_users, dcp.can_recommend_books
            FROM dealer_company_permissions dcp
            WHERE dcp.company_id = %s
            """
            cursor.execute(query, (user_info['dealer_company_id'],))
            permission = cursor.fetchone()
            if permission:
                user_permissions['can_register_exhibition'] = bool(permission['can_register_exhibition'])
                user_permissions['can_initiate_exhibition'] = bool(permission['can_initiate_exhibition'])
                user_permissions['can_invite_users'] = bool(permission['can_invite_users'])
                user_permissions['can_recommend_books'] = bool(permission['can_recommend_books'])
            # 检查推荐权限
            query = """
            SELECT 1 FROM recommendation_permissions
            WHERE dealer_id = %s AND permission_type = 'recommend'
            """
            cursor.execute(query, (user_id,))
            if cursor.fetchone():
                user_permissions['can_recommend_books'] = True

        if user_role == 'admin':
            nav_items = [
                {
                    'title': '首页',
                    'icon': 'fas fa-home',
                    'url': '/admin/dashboard'
                },
                {
                    'title': '系统管理',
                    'icon': 'fas fa-cog',
                    'sub_items': [
                        {
                            'title': '网站设置',
                            'icon': 'fas fa-cog',
                            'url': '/pc_admin_site_settings'
                        },
                        {
                            'title': '组织登录设置',
                            'icon': 'fas fa-sign-in-alt',
                            'url': '/pc_admin_organization_login_settings'
                        },
                        {
                            'title': '审计日志',
                            'icon': 'fas fa-history',
                            'url': '/pc_admin_manage_audit_logs'
                        }
                    ]
                },
                {
                    'title': '产品管理',
                    'icon': 'fas fa-book',
                    'sub_items': [
                        {
                            'title': '产品管理',
                            'icon': 'fas fa-book',
                            'url': '/admin_manage_samples'
                        },
                        {
                            'title': '后台书籍管理',
                            'icon': 'fas fa-database',
                            'url': '/pc_admin_manage_backend_books'
                        },
                        {
                            'title': '字段管理',
                            'icon': 'fas fa-cogs',
                            'url': '/pc_admin_manage_sample_fields'
                        },
                        {
                            'title': '价格管理',
                            'icon': 'fas fa-chart-line',
                            'url': '/admin_price_changes'
                        }
                    ]
                },
                {
                    'title': '申请管理',
                    'icon': 'fas fa-clipboard-check',
                    'sub_items': [
                        {
                            'title': '管理样书申请',
                            'icon': 'fas fa-clipboard-check',
                            'url': '/admin_manage_sample_requests'
                        },
                        {
                            'title': '管理课件申请',
                            'icon': 'fas fa-chalkboard',
                            'url': '/pc_admin_manage_courseware_requests'
                        },
                        {
                            'title': '管理报备申请',
                            'icon': 'fas fa-file-alt',
                            'url': '/admin_manage_report_requests'
                        }
                    ]
                },
                {
                    'title': '用户管理',
                    'icon': 'fas fa-users',
                    'sub_items': [
                        {
                            'title': '用户管理',
                            'icon': 'fas fa-users',
                            'url': '/pc_admin_manage_users'
                        },
                        {
                            'title': '用户权限管理',
                            'icon': 'fas fa-shield-alt',
                            'url': '/pc_admin_manage_user_permissions'
                        },
                        {
                            'title': '账号绑定管理',
                            'icon': 'fas fa-link',
                            'url': '/pc_admin_manage_account_bindings'
                        },
                        {
                            'title': '组织管理',
                            'icon': 'fas fa-building',
                            'url': '/pc_admin_manage_organizations'
                        },
                        {
                            'title': '邀请数据统计',
                            'icon': 'fas fa-chart-bar',
                            'url': '/admin/invitation_statistics'
                        }
                    ]
                },
                {
                    'title': '书展管理',
                    'icon': 'fas fa-calendar-alt',
                    'sub_items': [
                        {
                            'title': '书展管理',
                            'icon': 'fas fa-calendar-alt',
                            'url': '/pc_admin_manage_exhibitions'
                        },
                        {
                            'title': '书展黑白名单',
                            'icon': 'fas fa-list-alt',
                            'url': '/pc_admin_manage_exhibition_blacklist'
                        }
                    ]
                },
                {
                    'title': '换版推荐',
                    'icon': 'fas fa-exchange-alt',
                    'url': '/pc_admin_manage_book_recommendations'
                },
                {
                    'title': '系统配置',
                    'icon': 'fas fa-wrench',
                    'sub_items': [
                        {
                            'title': '邮件配置',
                            'icon': 'fas fa-envelope-open',
                            'url': '/pc_admin_email_configs'
                        }
                    ]
                }
            ]
            initial_page = '/admin/dashboard'
        elif user_role == 'teacher':
            nav_items = [
                {
                    'title': '教材巡展',
                    'icon': 'fas fa-map-marker-alt',
                    'url': '/pc_teacher_manage_exhibitions'
                },
                # {
                #     'title': '申请样书',
                #     'icon': 'fas fa-book',
                #     'url': '/pc_teacher_request_samples'
                # },
                {
                    'title': '申请样书',
                    'icon': 'fas fa-clipboard-list',
                    'url': '/pc_teacher_manage_requests'
                },
                {
                    'title': '申请课件',
                    'icon': 'fas fa-chalkboard',
                    'url': '/pc_teacher_courseware_requests'
                }
            ]
            initial_page = '/pc_teacher_manage_exhibitions'
        elif user_role == 'publisher':
            # 查询出版社的待处理申请数量
            pending_counts = {}

            # 样书申请待处理数量（按订单号统计，与页面显示一致）
            cursor.execute("""
                SELECT COUNT(DISTINCT sr.order_number) as count
                FROM sample_requests sr
                JOIN sample_books sb ON sr.textbook_id = sb.id
                WHERE sb.publisher_id = %s
                AND sr.status = 'pending'
                AND sr.order_number IS NOT NULL
            """, (user_id,))
            pending_counts['sample_requests'] = cursor.fetchone()['count']

            # 课件申请待处理数量
            cursor.execute("""
                SELECT COUNT(*) as count
                FROM courseware_requests cr
                JOIN sample_books sb ON cr.sample_book_id = sb.id
                WHERE sb.publisher_id = %s AND cr.status = 'pending'
            """, (user_id,))
            pending_counts['courseware_requests'] = cursor.fetchone()['count']

            # 推广报备待处理数量（通过sample_books关联）
            cursor.execute("""
                SELECT COUNT(*) as count
                FROM promotion_reports pr
                JOIN sample_books sb ON pr.sample_book_id = sb.id
                WHERE sb.publisher_id = %s AND pr.status = 'pending'
            """, (user_id,))
            pending_counts['promotion_reports'] = cursor.fetchone()['count']

            # 换版推荐待处理数量（只统计当前出版社还没有提交推荐结果的）
            cursor.execute("""
                SELECT COUNT(DISTINCT br.id) as count
                FROM book_recommendations br
                WHERE br.recommendation_type = 'external'
                AND br.status IN ('pending', 'in_progress')
                AND NOT EXISTS (
                    SELECT 1 FROM recommendation_results rr
                    WHERE rr.recommendation_id = br.id
                    AND rr.recommender_id = %s
                )
            """, (user_id,))
            pending_counts['book_recommendations'] = cursor.fetchone()['count']

            nav_items = [
                {
                    'title': '产品管理',
                    'icon': 'fas fa-book',
                    'url': '/pc_publisher_manage_samples'
                },
                {
                    'title': '样书申请',
                    'icon': 'fas fa-clipboard-list',
                    'url': '/pc_publisher_manage_sample_requests',
                    'badge_count': pending_counts['sample_requests']
                },
                {
                    'title': '课件申请',
                    'icon': 'fas fa-chalkboard',
                    'url': '/pc_publisher_courseware_requests',
                    'badge_count': pending_counts['courseware_requests']
                },
                {
                    'title': '推广报备',
                    'icon': 'fas fa-file-alt',
                    'url': '/pc_publisher_manage_report_requests',
                    'badge_count': pending_counts['promotion_reports']
                },
                {
                    'title': '订单管理',
                    'icon': 'fas fa-clipboard-list',
                    'url': '/pc_publisher_manage_orders'
                },
                {
                    'title': '分享清单',
                    'icon': 'fas fa-share-alt',
                    'url': '/my-shared-lists'
                },
                {
                    'title': '合作经销商',
                    'icon': 'fas fa-handshake',
                    'url': '/publisher/partnerships'
                }
            ]

            # 只有拥有费率查看权限的用户才能看到费率管理菜单
            if user_permissions['can_view_rate_info']:
                nav_items.append({
                    'title': '费率管理',
                    'icon': 'fas fa-percentage',
                    'url': '/pc_publisher_rates'
                })

            # 只有拥有书展报名权限的出版社才能看到书展管理菜单
            if user_permissions['can_register_exhibition']:
                nav_items.append({
                    'title': '教材巡展',
                    'icon': 'fas fa-calendar-alt',
                    'url': '/pc_publisher_manage_exhibitions'
                })

            # 检查是否有换版推荐权限，有权限的才显示书展审核菜单
            if user_permissions['can_recommend_books']:
                # 检查是否有协办书展需要审核
                cursor.execute("""
                    SELECT COUNT(*) as count
                    FROM book_exhibitions
                    WHERE co_organizer_type = 'publisher'
                    AND co_organizer_id = %s
                    AND status = 'pending_review'
                """, (user_info['publisher_company_id'],))
                pending_count = cursor.fetchone()['count']

                nav_items.append({
                    'title': '书展审核',
                    'icon': 'fas fa-gavel',
                    'url': '/co_organizer_exhibitions',
                    'badge_count': pending_count
                })

            # 只有有权限的出版社才能看到换版推荐功能
            if user_permissions['can_recommend_books']:
                nav_items.append({
                    'title': '换版推荐',
                    'icon': 'fas fa-exchange-alt',
                    'url': '/pc_publisher_book_recommendations',
                    'badge_count': pending_counts['book_recommendations']
                })

            initial_page = '/pc_publisher_manage_exhibitions'
        elif user_role == 'dealer':
            nav_items = [
                # {
                #     'title': '报备样书推广',
                #     'icon': 'fas fa-bullhorn',
                #     'url': '/pc_dealer_report_samples'
                # },
                {
                    'title': '推广报备',
                    'icon': 'fas fa-file-alt',
                    'url': '/pc_dealer_manage_reports'
                },
                {
                    'title': '订单管理',
                    'icon': 'fas fa-clipboard-list',
                    'url': '/pc_dealer_manage_orders'
                },
                {
                    'title': '分享清单',
                    'icon': 'fas fa-share-alt',
                    'url': '/my-shared-lists'
                }
            ]

            # 只有有权限的经销商才能看到书展相关菜单
            if user_permissions['can_register_exhibition'] or user_permissions['can_initiate_exhibition']:
                nav_items.append({
                    'title': '书展报名',
                    'icon': 'fas fa-calendar-alt',
                    'url': '/pc_dealer_manage_exhibitions'
                })

            # 检查是否有换版推荐权限，有权限的才显示书展审核菜单
            if user_permissions['can_recommend_books']:
                # 检查是否有协办书展需要审核
                cursor.execute("""
                    SELECT COUNT(*) as count
                    FROM book_exhibitions
                    WHERE co_organizer_type = 'dealer'
                    AND co_organizer_id = %s
                    AND status = 'pending_review'
                """, (user_info['dealer_company_id'],))
                pending_count = cursor.fetchone()['count']

                nav_items.append({
                    'title': '书展审核',
                    'icon': 'fas fa-gavel',
                    'url': '/co_organizer_exhibitions',
                    'badge_count': pending_count
                })

            # 只有有邀请权限的经销商才能看到邀请注册菜单
            if user_permissions['can_invite_users']:
                nav_items.append({
                    'title': '邀请注册',
                    'icon': 'fas fa-user-plus',
                    'url': '/dealer/invitation_codes'
                })

            # 只有有推荐权限的经销商才能看到推荐换版菜单
            if user_permissions['can_recommend_books']:
                nav_items.append({
                    'title': '换版推荐',
                    'icon': 'fas fa-sync-alt',
                    'url': '/pc_dealer_book_recommendations'
                })

            initial_page = '/pc_dealer_manage_reports'

        # 共同菜单项 - 个人中心
        nav_items.append({
            'title': '个人设置',
            'icon': 'fas fa-user-cog',
            'url': '/edit_profile'
        })

        cursor.close()
        connection.close()

        return jsonify({
            "status": "success",
            "user_role": user_role,
            "name": display_name,
            "nav_items": nav_items,
            "initial_page": initial_page,
            "permissions": user_permissions
        })

    except Exception as e:
        return jsonify({"status": "error", "message": str(e)})

@common_bp.route('/logout', methods=['POST'])
def logout():
    """
    用户注销
    根据用户角色重定向到对应的登录页面
    """
    try:
        # 获取当前用户角色和用户ID
        user_role = session.get('role')
        user_id = session.get('user_id')
        username = session.get('username')

        # 记录注销日志（在清除session之前）
        AuditLogService.log_action(
            action_type=AuditLogService.ActionType.LOGOUT,
            description=f"{user_role}用户注销",
            details={
                'previous_role': user_role,
                'username': username
            },
            user_id=user_id
        )

        # 清除所有会话数据
        session.clear()

        # 如果没有角色信息，返回默认404
        if not user_role:
            return jsonify({"message": "注销成功", "status": "success", "redirect": "/login"})

        # 根据用户角色查询对应的login_url
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 查询该角色的login_url
        sql = """
            SELECT login_url
            FROM site_settings
            WHERE user_role = %s AND is_active = 1
        """
        cursor.execute(sql, (user_role,))
        result = cursor.fetchone()

        cursor.close()
        connection.close()

        # 如果找到了对应的login_url，带上url参数重定向
        if result and result['login_url']:
            redirect_url = f"/login?url={result['login_url']}"
        else:
            # 如果没有找到，重定向到默认登录（会显示404）
            redirect_url = "/login"

        return jsonify({
            "message": "注销成功",
            "status": "success",
            "redirect": redirect_url
        })

    except Exception as e:
        # 发生错误时清除session并返回默认登录
        session.clear()
        print(f"注销错误: {str(e)}")
        return jsonify({
            "message": "注销成功",
            "status": "success",
            "redirect": "/login"
        })

@common_bp.route('/check_username_change_permission', methods=['GET'])
def check_username_change_permission():
    """
    检查用户是否可以修改用户名
    返回:
        can_change: 是否可以修改
        last_change_time: 上次修改时间
        next_change_time: 下次可修改时间
    """
    if 'user_id' not in session:
        return jsonify({"status": "error", "message": "用户未登录"})

    try:
        user_id = session.get('user_id')

        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 查询用户名修改历史
        query = """
        SELECT change_time, old_username, new_username
        FROM username_change_history
        WHERE user_id = %s
        """
        cursor.execute(query, (user_id,))
        history = cursor.fetchone()

        can_change = True
        last_change_time = None
        next_change_time = None

        if history:
            # 如果有修改记录，检查是否满足半年间隔
            last_change_time = history['change_time']
            # 计算下次可修改时间（半年后）
            next_change_time = last_change_time + datetime.timedelta(days=180)
            current_time = datetime.datetime.now()

            if current_time < next_change_time:
                can_change = False

        cursor.close()
        connection.close()

        return jsonify({
            "status": "success",
            "can_change": can_change,
            "last_change_time": last_change_time.strftime('%Y-%m-%d %H:%M:%S') if last_change_time else None,
            "next_change_time": next_change_time.strftime('%Y-%m-%d %H:%M:%S') if next_change_time else None
        })

    except Exception as e:
        return jsonify({"status": "error", "message": f"检查权限失败: {str(e)}"})

@common_bp.route('/change_username', methods=['POST'])
def change_username():
    """
    修改用户名
    请求数据:
        new_username: 新用户名
    返回:
        修改结果
    """
    if 'user_id' not in session:
        return jsonify({"status": "error", "message": "用户未登录"})

    try:
        data = request.get_json() if request.is_json else request.form.to_dict()
        new_username = data.get('new_username', '').strip()

        if not new_username:
            return jsonify({"status": "error", "message": "新用户名不能为空"})

        # 验证用户名格式（可以根据需要调整规则）
        if len(new_username) < 3 or len(new_username) > 50:
            return jsonify({"status": "error", "message": "用户名长度必须在3-50个字符之间"})

        # 检查用户名是否包含特殊字符
        if not re.match(r'^[a-zA-Z0-9_\u4e00-\u9fa5]+$', new_username):
            return jsonify({"status": "error", "message": "用户名只能包含字母、数字、下划线和中文字符"})

        user_id = session.get('user_id')

        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 获取当前用户信息
        query = "SELECT username FROM users WHERE user_id = %s"
        cursor.execute(query, (user_id,))
        current_user = cursor.fetchone()

        if not current_user:
            cursor.close()
            connection.close()
            return jsonify({"status": "error", "message": "用户不存在"})

        current_username = current_user['username']

        # 检查新用户名是否与当前用户名相同
        if new_username == current_username:
            cursor.close()
            connection.close()
            return jsonify({"status": "error", "message": "新用户名与当前用户名相同"})

        # 检查新用户名是否已被其他用户使用
        query = "SELECT user_id FROM users WHERE username = %s AND user_id != %s"
        cursor.execute(query, (new_username, user_id))
        if cursor.fetchone():
            cursor.close()
            connection.close()
            return jsonify({"status": "error", "message": "该用户名已被使用，请选择其他用户名"})

        # 检查修改权限
        query = """
        SELECT change_time
        FROM username_change_history
        WHERE user_id = %s
        """
        cursor.execute(query, (user_id,))
        history = cursor.fetchone()

        if history:
            # 检查是否满足半年间隔
            last_change_time = history['change_time']
            next_change_time = last_change_time + datetime.timedelta(days=180)
            current_time = datetime.datetime.now()

            if current_time < next_change_time:
                cursor.close()
                connection.close()
                return jsonify({
                    "status": "error",
                    "message": f"距离上次修改用户名不足半年，下次可修改时间：{next_change_time.strftime('%Y-%m-%d %H:%M:%S')}"
                })

        # 开始事务
        try:
            connection.begin()

            # 更新用户表中的用户名
            query = "UPDATE users SET username = %s WHERE user_id = %s"
            cursor.execute(query, (new_username, user_id))

            # 插入或更新修改历史记录
            if history:
                # 更新现有记录
                query = """
                UPDATE username_change_history
                SET old_username = %s, new_username = %s, change_time = NOW()
                WHERE user_id = %s
                """
                cursor.execute(query, (current_username, new_username, user_id))
            else:
                # 插入新记录
                query = """
                INSERT INTO username_change_history (user_id, old_username, new_username, change_time)
                VALUES (%s, %s, %s, NOW())
                """
                cursor.execute(query, (user_id, current_username, new_username))

            connection.commit()

            # 更新session中的用户名
            session['username'] = new_username

            cursor.close()
            connection.close()

            return jsonify({"status": "success", "message": "用户名修改成功"})

        except Exception as e:
            connection.rollback()
            raise e

    except Exception as e:
        return jsonify({"status": "error", "message": f"修改用户名失败: {str(e)}"})

@common_bp.route('/get_username_change_history', methods=['GET'])
def get_username_change_history():
    """
    获取用户名修改历史
    返回:
        修改历史记录
    """
    if 'user_id' not in session:
        return jsonify({"status": "error", "message": "用户未登录"})

    try:
        user_id = session.get('user_id')

        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 查询修改历史
        query = """
        SELECT old_username, new_username, change_time
        FROM username_change_history
        WHERE user_id = %s
        """
        cursor.execute(query, (user_id,))
        history = cursor.fetchone()

        cursor.close()
        connection.close()

        result = None
        if history:
            result = {
                "old_username": history['old_username'],
                "new_username": history['new_username'],
                "change_time": history['change_time'].strftime('%Y-%m-%d %H:%M:%S')
            }

        return jsonify({
            "status": "success",
            "history": result
        })

    except Exception as e:
        return jsonify({"status": "error", "message": f"获取修改历史失败: {str(e)}"})

@common_bp.route('/check_role_switch_permission', methods=['GET'])
def check_role_switch_permission():
    """
    检查用户是否有切换角色的权限
    返回:
        can_switch: 是否可以切换角色
        target_role: 目标角色
        target_user_info: 目标用户信息
    """
    if 'user_id' not in session:
        return jsonify({"status": "error", "message": "用户未登录"})

    try:
        user_id = session.get('user_id')
        user_role = session.get('role')

        # 只有经销商和出版社用户可以切换角色
        if user_role not in ['dealer', 'publisher']:
            return jsonify({"status": "success", "can_switch": False, "message": "当前角色不支持切换"})

        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 根据当前角色查找绑定关系
        if user_role == 'dealer':
            # 经销商用户查找绑定的出版社用户
            query = """
            SELECT ab.publisher_user_id as target_user_id, u.username, u.name, pc.name as company_name
            FROM account_bindings ab
            JOIN users u ON ab.publisher_user_id = u.user_id
            LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id
            WHERE ab.dealer_user_id = %s AND ab.status = 1 AND u.role = 'publisher'
            """
            target_role = 'publisher'
        else:
            # 出版社用户查找绑定的经销商用户
            query = """
            SELECT ab.dealer_user_id as target_user_id, u.username, u.name, dc.name as company_name
            FROM account_bindings ab
            JOIN users u ON ab.dealer_user_id = u.user_id
            LEFT JOIN dealer_companies dc ON u.dealer_company_id = dc.id
            WHERE ab.publisher_user_id = %s AND ab.status = 1 AND u.role = 'dealer'
            """
            target_role = 'dealer'

        cursor.execute(query, (user_id,))
        binding_result = cursor.fetchone()

        cursor.close()
        connection.close()

        if binding_result:
            return jsonify({
                "status": "success",
                "can_switch": True,
                "target_role": target_role,
                "target_user_info": {
                    "user_id": binding_result['target_user_id'],
                    "username": binding_result['username'],
                    "name": binding_result['name'],
                    "company_name": binding_result['company_name']
                }
            })
        else:
            return jsonify({"status": "success", "can_switch": False, "message": "未找到绑定的账号"})

    except Exception as e:
        return jsonify({"status": "error", "message": f"检查切换权限失败: {str(e)}"})

@common_bp.route('/switch_role', methods=['POST'])
def switch_role():
    """
    执行角色切换
    返回:
        切换结果和重定向URL
    """
    if 'user_id' not in session:
        return jsonify({"status": "error", "message": "用户未登录"})

    try:
        current_user_id = session.get('user_id')
        current_role = session.get('role')

        # 只有经销商和出版社用户可以切换角色
        if current_role not in ['dealer', 'publisher']:
            return jsonify({"status": "error", "message": "当前角色不支持切换"})

        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 根据当前角色查找绑定的目标用户
        if current_role == 'dealer':
            query = """
            SELECT ab.publisher_user_id as target_user_id
            FROM account_bindings ab
            WHERE ab.dealer_user_id = %s AND ab.status = 1
            """
        else:
            query = """
            SELECT ab.dealer_user_id as target_user_id
            FROM account_bindings ab
            WHERE ab.publisher_user_id = %s AND ab.status = 1
            """

        cursor.execute(query, (current_user_id,))
        binding_result = cursor.fetchone()

        if not binding_result:
            cursor.close()
            connection.close()
            return jsonify({"status": "error", "message": "未找到绑定的账号"})

        target_user_id = binding_result['target_user_id']

        # 获取目标用户的详细信息
        query = """
        SELECT u.user_id, u.username, u.name, u.phone_number, u.contact_info, u.role,
               u.teacher_school_id, u.publisher_company_id, u.dealer_company_id
        FROM users u
        WHERE u.user_id = %s
        """
        cursor.execute(query, (target_user_id,))
        target_user = cursor.fetchone()

        if not target_user:
            cursor.close()
            connection.close()
            return jsonify({"status": "error", "message": "目标用户不存在"})

        # 获取目标用户的显示名称
        target_role = target_user['role']
        display_name = target_user['username']  # 默认使用用户名

        if target_role == 'publisher' and target_user.get('publisher_company_id'):
            query = "SELECT name FROM publisher_companies WHERE id = %s"
            cursor.execute(query, (target_user['publisher_company_id'],))
            company = cursor.fetchone()
            if company:
                display_name = company['name']

            # 检查切换到的出版社用户是否有目录，没有则创建默认目录（兼容旧版本）
            try:
                cursor.execute("SELECT COUNT(*) as count FROM directories WHERE publisher_id = %s", (target_user_id,))
                directory_count = cursor.fetchone()

                if directory_count and directory_count['count'] == 0:
                    # 没有目录，创建默认目录
                    cursor.execute("""
                        INSERT INTO directories (name, parent_id, publisher_id)
                        VALUES (%s, %s, %s)
                    """, ('默认目录', None, target_user_id))
                    connection.commit()
                    print(f"为出版社用户 {target_user_id} 创建默认目录成功 (角色切换时检测)")
            except Exception as e:
                print(f"为出版社用户 {target_user_id} 创建默认目录失败 (角色切换时检测): {str(e)}")
                # 目录创建失败不影响角色切换成功
        elif target_role == 'dealer' and target_user.get('dealer_company_id'):
            query = "SELECT name FROM dealer_companies WHERE id = %s"
            cursor.execute(query, (target_user['dealer_company_id'],))
            company = cursor.fetchone()
            if company:
                display_name = company['name']

        cursor.close()
        connection.close()

        # 更新session信息
        session['user_id'] = target_user['user_id']
        session['username'] = target_user['username']
        session['role'] = target_user['role']
        session['name'] = display_name

        return jsonify({
            "status": "success",
            "message": f"已切换到{target_role}角色",
            "redirect": "/dashboard",
            "new_role": target_role,
            "new_username": target_user['username'],
            "new_display_name": display_name
        })

    except Exception as e:
        return jsonify({"status": "error", "message": f"角色切换失败: {str(e)}"})


@common_bp.route('/get_teacher_info', methods=['GET'])
def get_teacher_info():
    """
    获取教师详细信息
    返回:
        教师的院系、职务、职称、性别信息
    """
    if 'user_id' not in session:
        return jsonify({"status": "error", "message": "用户未登录"})

    user_role = session.get('role')
    if user_role != 'teacher':
        return jsonify({"status": "error", "message": "只有教师用户可以访问此接口"})

    try:
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        user_id = session.get('user_id')

        # 查询教师详细信息
        query = """
        SELECT department, position, title, gender
        FROM teacher_info
        WHERE teacher_id = %s
        """
        cursor.execute(query, (user_id,))
        teacher_info = cursor.fetchone()

        cursor.close()
        connection.close()

        return jsonify({
            "status": "success",
            "teacher_info": teacher_info
        })
    except Exception as e:
        return jsonify({"status": "error", "message": f"获取教师信息失败: {str(e)}"})

@common_bp.route('/save_teacher_info', methods=['POST'])
def save_teacher_info():
    """
    保存教师详细信息
    请求数据:
        department: 院系
        position: 职务
        title: 职称
        gender: 性别
    返回:
        保存结果
    """
    if 'user_id' not in session:
        return jsonify({"status": "error", "message": "用户未登录"})

    user_role = session.get('role')
    if user_role != 'teacher':
        return jsonify({"status": "error", "message": "只有教师用户可以访问此接口"})

    try:
        data = request.get_json()
        user_id = session.get('user_id')

        department = data.get('department', '').strip()
        position = data.get('position', '').strip()
        title = data.get('title', '').strip()
        gender = data.get('gender', '').strip()

        # 验证性别字段
        if gender and gender not in ['男', '女']:
            return jsonify({"status": "error", "message": "性别只能选择男或女"})

        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 检查是否已存在教师信息记录
        check_query = "SELECT id FROM teacher_info WHERE teacher_id = %s"
        cursor.execute(check_query, (user_id,))
        existing_record = cursor.fetchone()

        if existing_record:
            # 更新现有记录
            update_query = """
            UPDATE teacher_info
            SET department = %s, position = %s, title = %s, gender = %s, updated_at = CURRENT_TIMESTAMP
            WHERE teacher_id = %s
            """
            cursor.execute(update_query, (department, position, title, gender, user_id))
        else:
            # 插入新记录
            insert_query = """
            INSERT INTO teacher_info (teacher_id, department, position, title, gender)
            VALUES (%s, %s, %s, %s, %s)
            """
            cursor.execute(insert_query, (user_id, department, position, title, gender))

        connection.commit()
        cursor.close()
        connection.close()

        return jsonify({"status": "success", "message": "教师信息保存成功"})
    except Exception as e:
        return jsonify({"status": "error", "message": f"保存教师信息失败: {str(e)}"})

@common_bp.route('/edit_profile', methods=['POST'])
def edit_profile():
    """
    修改用户个人资料
    请求数据:
        name: 姓名
        nickname: 昵称
        phone_number: 手机号
        email: 邮箱
        school_id: 学校ID (仅教师用户可修改)
    返回:
        修改结果
    """
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "用户未登录"})

    user_id = session.get('user_id')
    user_role = session.get('role')
    name = request.form.get('name') or request.json.get('name')
    nickname = request.form.get('nickname') or request.json.get('nickname')
    phone_number = request.form.get('phone_number') or request.json.get('phone_number')
    email = request.form.get('email') or request.json.get('email')
    school_id = request.form.get('school_id') or request.json.get('school_id')  # 新增学校ID参数

    # 确保获取到数据
    if not (name and nickname and phone_number and email):
        return jsonify({"code": 1, "message": "姓名、昵称、手机号和邮箱不能为空"})

    # 验证手机号格式
    if not re.match(r'^1[3-9]\d{9}$', phone_number):
        return jsonify({"code": 1, "message": "手机号格式不正确"})

    # 验证邮箱格式
    if not re.match(r'^[^\s@]+@[^\s@]+\.[^\s@]+$', email):
        return jsonify({"code": 1, "message": "请输入正确的邮箱格式"})

    # 建立数据库连接
    connection = get_db_connection()

    try:
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            # 检查手机号是否已存在且不是当前用户
            cursor.execute("SELECT user_id FROM users WHERE phone_number = %s AND user_id != %s",
                          (phone_number, user_id))
            if cursor.fetchone():
                return jsonify({"code": 1, "message": "该手机号已被其他用户使用"})

            # 检查邮箱是否已存在且不是当前用户
            cursor.execute("SELECT user_id FROM users WHERE email = %s AND user_id != %s",
                          (email, user_id))
            if cursor.fetchone():
                return jsonify({"code": 1, "message": "该邮箱已被其他用户使用"})

            # 如果是教师用户且提供了学校ID，验证学校是否存在
            if user_role == 'teacher' and school_id:
                cursor.execute("SELECT id FROM schools WHERE id = %s", (school_id,))
                if not cursor.fetchone():
                    return jsonify({"code": 1, "message": "选择的学校不存在"})

            # 更新用户信息
            if user_role == 'teacher' and school_id:
                # 教师用户可以更新学校信息
                sql_update = "UPDATE users SET name = %s, nickname = %s, phone_number = %s, email = %s, teacher_school_id = %s WHERE user_id = %s"
                cursor.execute(sql_update, (name, nickname, phone_number, email, school_id, user_id))
            else:
                # 其他用户只更新基本信息
                sql_update = "UPDATE users SET name = %s, nickname = %s, phone_number = %s, email = %s WHERE user_id = %s"
                cursor.execute(sql_update, (name, nickname, phone_number, email, user_id))

            connection.commit()

            # 更新session中的名称
            session['name'] = name

            return jsonify({"code": 0, "message": "个人资料修改成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"修改失败: {str(e)}"})
    finally:
        connection.close()

@common_bp.route('/change_password', methods=['POST'])
def change_password():
    """
    修改用户密码
    请求数据:
        current_password: 当前密码
        new_password: 新密码
        confirm_password: 确认新密码
    返回:
        修改结果
    """
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "用户未登录"})

    data = request.form if request.form else request.json

    current_password = data.get('current_password')
    new_password = data.get('new_password')
    confirm_password = data.get('confirm_password')

    # 验证数据完整性
    if not all([current_password, new_password, confirm_password]):
        return jsonify({"code": 1, "message": "所有密码字段都必须填写"})

    # 确认两次输入的新密码一致
    if new_password != confirm_password:
        return jsonify({"code": 1, "message": "两次输入的新密码不一致"})

    # 验证新密码长度
    if len(new_password) < 6:
        return jsonify({"code": 1, "message": "新密码长度不能少于6个字符"})

    user_id = session.get('user_id')

    # 建立数据库连接
    connection = get_db_connection()

    try:
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            # 验证当前密码
            cursor.execute("SELECT password FROM users WHERE user_id = %s", (user_id,))
            user = cursor.fetchone()

            if not user or not check_password_hash(user['password'], current_password):
                return jsonify({"code": 1, "message": "当前密码不正确"})

            # 生成新密码的哈希值
            hashed_password = generate_password_hash(new_password)

            # 更新密码
            cursor.execute("UPDATE users SET password = %s WHERE user_id = %s",
                          (hashed_password, user_id))
            connection.commit()

            return jsonify({"code": 0, "message": "密码修改成功"})
    except Exception as e:
        return jsonify({"code": 1, "message": f"修改密码失败: {str(e)}"})
    finally:
        connection.close()

# 样书相关API接口

# 新的经销商费率计算系统

def get_dealer_organization_info(cursor, user_id):
    """获取经销商用户的组织信息"""
    sql = """
        SELECT d.organization_id, d.company_id, do.name as organization_name, dc.name as company_name
        FROM dealers d
        LEFT JOIN dealer_organizations do ON d.organization_id = do.id
        LEFT JOIN dealer_companies dc ON d.company_id = dc.id
        WHERE d.user_id = %s
    """
    cursor.execute(sql, (user_id,))
    return cursor.fetchone()

def get_default_rate_adjustments(cursor):
    """获取默认费率加点配置"""
    sql = """
        SELECT shipping_adjustment, settlement_adjustment, promotion_adjustment
        FROM default_rate_adjustments
        ORDER BY id DESC
        LIMIT 1
    """
    cursor.execute(sql)
    result = cursor.fetchone()

    if result:
        return {
            'shipping': float(result['shipping_adjustment']),
            'settlement': float(result['settlement_adjustment']),
            'promotion': float(result['promotion_adjustment'])
        }

    return {'shipping': 0.0, 'settlement': 0.0, 'promotion': 0.0}

def get_organization_rate_adjustments(cursor, organization_id):
    """获取经销商组织级别费率加点配置"""
    if not organization_id:
        return {'shipping': 0.0, 'settlement': 0.0, 'promotion': 0.0}

    # 注意：这里的organization_id实际上是company_id
    sql = """
        SELECT shipping_adjustment, settlement_adjustment, promotion_adjustment
        FROM dealer_organization_rate_adjustments
        WHERE company_id = %s
    """
    cursor.execute(sql, (organization_id,))
    result = cursor.fetchone()

    if result:
        return {
            'shipping': float(result['shipping_adjustment']) if result['shipping_adjustment'] is not None else 0.0,
            'settlement': float(result['settlement_adjustment']) if result['settlement_adjustment'] is not None else 0.0,
            'promotion': float(result['promotion_adjustment']) if result['promotion_adjustment'] is not None else 0.0
        }

    return {'shipping': 0.0, 'settlement': 0.0, 'promotion': 0.0}

def get_channel_rate_adjustments(cursor, organization_id, sample_book_id):
    """获取渠道费率加点配置"""
    if not organization_id or not sample_book_id:
        return None

    sql = """
        SELECT shipping_adjustment, settlement_adjustment, promotion_adjustment
        FROM channel_rate_adjustments
        WHERE organization_id = %s AND sample_book_id = %s
    """
    cursor.execute(sql, (organization_id, sample_book_id))
    result = cursor.fetchone()

    if result:
        return {
            'shipping': float(result['shipping_adjustment']) if result['shipping_adjustment'] is not None else None,
            'settlement': float(result['settlement_adjustment']) if result['settlement_adjustment'] is not None else None,
            'promotion': float(result['promotion_adjustment']) if result['promotion_adjustment'] is not None else None
        }

    return None

def calculate_dealer_rates(cursor, organization_id, sample_book_data):
    """
    计算经销商看到的费率信息

    Args:
        cursor: 数据库游标
        organization_id: 经销商组织ID
        sample_book_data: 样书数据（包含原始费率）

    Returns:
        dict: 调整后的费率信息
    """
    if not sample_book_data:
        return sample_book_data

    # 获取原始费率
    original_shipping = sample_book_data.get('shipping_discount')
    original_settlement = sample_book_data.get('settlement_discount')
    original_promotion = sample_book_data.get('promotion_rate')
    sample_book_id = sample_book_data.get('id')

    # 处理空值情况
    if original_shipping is None or original_settlement is None:
        # 如果发货折扣或结算折扣任一为空，相关费率返回空
        return {
            **sample_book_data,
            'shipping_discount': None,
            'settlement_discount': None,
            'promotion_rate': None,
            'rate_calculation_note': '供应商未完整填写费率信息'
        }

    # 转换为浮点数
    original_shipping = float(original_shipping)
    original_settlement = float(original_settlement)

    # 如果没有填写推广费率，先计算原始推广费率
    if original_promotion is None:
        # 使用原始发货折扣和原始结算折扣计算原始推广费率
        original_promotion = max(0, original_shipping - original_settlement)
        promotion_rate_source = 'calculated'
    else:
        original_promotion = float(original_promotion)
        promotion_rate_source = 'manual'

    # 获取渠道加点配置
    channel_adjustments = get_channel_rate_adjustments(cursor, organization_id, sample_book_id)

    if channel_adjustments:
        # 使用渠道加点（优先级最高）
        final_shipping = original_shipping
        final_settlement = original_settlement
        final_promotion = original_promotion

        # 只对有设置的渠道加点进行调整
        if channel_adjustments['shipping'] is not None:
            final_shipping += channel_adjustments['shipping']
        else:
            # 没有设置渠道发货加点，使用默认+级别加点
            default_adj = get_default_rate_adjustments(cursor)
            org_adj = get_organization_rate_adjustments(cursor, organization_id)
            final_shipping += (default_adj['shipping'] + org_adj['shipping'])

        if channel_adjustments['settlement'] is not None:
            final_settlement += channel_adjustments['settlement']
        else:
            # 没有设置渠道结算加点，使用默认+级别加点
            default_adj = get_default_rate_adjustments(cursor)
            org_adj = get_organization_rate_adjustments(cursor, organization_id)
            final_settlement += (default_adj['settlement'] + org_adj['settlement'])

        if channel_adjustments['promotion'] is not None:
            final_promotion += channel_adjustments['promotion']
        else:
            # 没有设置渠道推广加点，使用默认+级别加点
            default_adj = get_default_rate_adjustments(cursor)
            org_adj = get_organization_rate_adjustments(cursor, organization_id)
            final_promotion += (default_adj['promotion'] + org_adj['promotion'])

        adjustment_type = 'channel'
    else:
        # 使用默认加点 + 级别加点
        default_adjustments = get_default_rate_adjustments(cursor)
        org_adjustments = get_organization_rate_adjustments(cursor, organization_id)

        final_shipping = original_shipping + default_adjustments['shipping'] + org_adjustments['shipping']
        final_settlement = original_settlement + default_adjustments['settlement'] + org_adjustments['settlement']
        final_promotion = original_promotion + default_adjustments['promotion'] + org_adjustments['promotion']

        adjustment_type = 'default_and_level'

    # 确保费率不为负数
    final_shipping = max(0, final_shipping)
    final_settlement = max(0, final_settlement)
    final_promotion = max(0, final_promotion)

    # 返回调整后的数据
    return {
        **sample_book_data,
        'shipping_discount': round(final_shipping, 4),
        'settlement_discount': round(final_settlement, 4),
        'promotion_rate': round(final_promotion, 4),
        'promotion_rate_calculated': round(final_promotion, 4),  # 为前端兼容性添加
        'promotion_rate_source': promotion_rate_source,
        'adjustment_type': adjustment_type,
        'original_shipping_discount': original_shipping,
        'original_settlement_discount': original_settlement,
        'original_promotion_rate': original_promotion
    }

# 兼容性函数：保持旧接口可用
def get_dealer_info(cursor, user_id):
    """获取经销商用户的级别和加点值（兼容性函数，已废弃）"""
    sql = """
        SELECT d.customer_level, d.point_value
        FROM dealers d
        WHERE d.user_id = %s
    """
    cursor.execute(sql, (user_id,))
    dealer_info = cursor.fetchone()

    if not dealer_info:
        return None, None

    customer_level = dealer_info.get('customer_level', 3)
    point_value = dealer_info.get('point_value')

    # 如果加点值为空，则按规则计算
    if point_value is None:
        if customer_level <= 3:
            point_value = 3
        else:
            point_value = customer_level

    return customer_level, point_value

# 新的统一费率处理函数
def process_dealer_rates_for_samples(cursor, organization_id, samples):
    """
    为样书列表统一处理经销商费率计算

    Args:
        cursor: 数据库游标
        organization_id: 经销商组织ID
        samples: 样书列表

    Returns:
        list: 处理后的样书列表
    """
    if not samples or not organization_id:
        return samples

    processed_samples = []
    for sample in samples:
        processed_sample = calculate_dealer_rates(cursor, organization_id, sample)
        processed_samples.append(processed_sample)

    return processed_samples

# 兼容性函数：保持旧接口可用（已废弃，建议使用新函数）
def process_discount_rates_and_promotion(samples, point_value=None):
    """统一处理推广费率计算和加点值调整（兼容性函数，已废弃）"""
    if not samples:
        return samples

    for sample in samples:
        # 先处理推广费率的计算和来源标识（在加点值调整之前）
        original_promotion_rate = sample.get('promotion_rate')
        shipping_discount = float(sample['shipping_discount']) if sample.get('shipping_discount') is not None else None
        settlement_discount = float(sample['settlement_discount']) if sample.get('settlement_discount') is not None else None

        if original_promotion_rate is not None:
            # 用户手动填写的推广费率
            sample['promotion_rate_source'] = 'manual'
            sample['promotion_rate_calculated'] = float(original_promotion_rate)
        elif shipping_discount is not None and settlement_discount is not None:
            # 系统计算的推广费率：发货折扣 - 结算折扣
            calculated_rate = shipping_discount - settlement_discount
            sample['promotion_rate_source'] = 'calculated'
            sample['promotion_rate_calculated'] = max(0, calculated_rate)
        else:
            # 无法计算推广费率
            sample['promotion_rate_source'] = 'none'
            sample['promotion_rate_calculated'] = None

        # 如果有加点值，需要调整费率
        if point_value is not None and shipping_discount is not None:
            point_value_decimal = point_value / 100.0

            # 发货折扣不变
            # 结算折扣需要加上加点值
            if settlement_discount is not None:
                sample['settlement_discount'] = round(settlement_discount + point_value_decimal, 4)

            # 推广费率需要根据来源进行调整
            if sample['promotion_rate_source'] == 'manual' and sample['promotion_rate_calculated'] is not None:
                # 用户手动填写的推广费率，需要减去加点值
                sample['promotion_rate_calculated'] = round(max(0, sample['promotion_rate_calculated'] - point_value_decimal), 4)
            elif sample['promotion_rate_source'] == 'calculated' and sample['promotion_rate_calculated'] is not None:
                # 系统计算的推广费率：发货折扣 - 调整后的结算折扣
                adjusted_settlement_discount = float(sample['settlement_discount'])
                sample['promotion_rate_calculated'] = round(max(0, shipping_discount - adjusted_settlement_discount), 4)

        # 保持兼容性，设置 promotion_rate 字段
        sample['promotion_rate'] = sample['promotion_rate_calculated']

    return samples

@common_bp.route('/get_all_samples', methods=['GET'])
def get_all_samples():
    """获取所有样书，按照合作状态、获奖情况排序"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})

    search = request.args.get('search', '')
    user_id = session.get('user_id')
    user_role = session.get('role')

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 获取经销商组织信息
            organization_id = None
            if user_role == 'dealer':
                from app.users.dealer import get_dealer_organization_id
                organization_id = get_dealer_organization_id(cursor, user_id)

            sql = """
                SELECT sb.id, sb.name, sb.author, sb.isbn, sb.price, sb.award_info, sb.publisher_name,
                       sb.publisher_id, sb.level, sb.book_type, sb.national_regulation, sb.provincial_regulation,
                       sb.national_regulation_level_id, sb.provincial_regulation_level_id,
                       sb.material_type, sb.attachment_link, sb.publication_date,
                       sb.sample_download_url, sb.resource_download_url, sb.online_reading_url,
                       d.name as directory_name,
                       nrl.name as national_regulation_level_name,
                       prl.name as provincial_regulation_level_name,
                       (SELECT GROUP_CONCAT(bf.name SEPARATOR ', ')
                        FROM sample_book_features sbf
                        JOIN book_features bf ON sbf.feature_id = bf.id
                        WHERE sbf.sample_id = sb.id) as feature_name,
                       CASE
                           WHEN sb.cooperation_status = 1 AND sb.award_info IS NOT NULL THEN 1
                           WHEN sb.cooperation_status = 1 AND sb.award_info IS NULL THEN 2
                           WHEN sb.cooperation_status = 0 AND sb.award_info IS NOT NULL THEN 3
                           ELSE 4
                       END AS priority
            """

            # 如果是经销商用户或管理员用户，添加折扣率字段
            if user_role in ['dealer', 'admin']:
                sql += ", sb.shipping_discount, sb.settlement_discount, sb.promotion_rate"

            sql += """
                FROM sample_books sb
                LEFT JOIN directories d ON sb.parent_id = d.id
                LEFT JOIN users u ON sb.publisher_id = u.user_id
                LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id
                LEFT JOIN national_regulation_levels nrl ON sb.national_regulation_level_id = nrl.id
                LEFT JOIN provincial_regulation_levels prl ON sb.provincial_regulation_level_id = prl.id
                WHERE (
                    sb.name LIKE %s OR
                    sb.author LIKE %s OR
                    sb.isbn LIKE %s OR
                    %s = ''
                )
            """

            # 如果是出版社用户，只显示自己公司的样书
            if user_role == 'publisher':
                # 获取当前用户的公司ID
                cursor.execute("SELECT publisher_company_id FROM users WHERE user_id = %s", (user_id,))
                user_company = cursor.fetchone()
                if user_company and user_company['publisher_company_id']:
                    sql += " AND u.publisher_company_id = %s ORDER BY priority, sb.name"
                    cursor.execute(sql, (f'%{search}%', f'%{search}%', f'%{search}%', search, user_company['publisher_company_id']))
                else:
                    # 如果没有公司ID，不显示任何样书
                    sql += " AND 1=0 ORDER BY priority, sb.name"
                    cursor.execute(sql, (f'%{search}%', f'%{search}%', f'%{search}%', search))
            else:
                sql += " ORDER BY priority, sb.name"
                cursor.execute(sql, (f'%{search}%', f'%{search}%', f'%{search}%', search))
            samples = cursor.fetchall()

            # 格式化日期字段
            for sample in samples:
                if sample.get('publication_date'):
                    sample['publication_date'] = sample['publication_date'].strftime('%Y-%m-%d')

            # 如果是经销商用户，处理费率计算和权限控制
            if user_role == 'dealer':
                # 使用新的统一费率处理函数
                if organization_id:
                    samples = process_dealer_rates_for_samples(cursor, organization_id, samples)
                else:
                    # 如果没有组织ID，保持原始费率不变
                    pass

                # 检查经销商的费率查看权限
                from app.users.admin import check_user_permission
                can_view_shipping_discount = check_user_permission(user_id, 'view_shipping_discount')
                can_view_settlement_discount = check_user_permission(user_id, 'view_settlement_discount')
                can_view_promotion_rate = check_user_permission(user_id, 'view_promotion_rate')

                # 根据权限过滤费率信息
                for sample in samples:
                    if not can_view_shipping_discount:
                        sample.pop('shipping_discount', None)
                    if not can_view_settlement_discount:
                        sample.pop('settlement_discount', None)
                    if not can_view_promotion_rate:
                        sample.pop('promotion_rate', None)
                        sample.pop('promotion_rate_source', None)
                        sample.pop('promotion_rate_calculated', None)

                # 返回权限信息给前端
                return jsonify({
                    "code": 0,
                    "data": samples,
                    "permissions": {
                        "can_view_shipping_discount": can_view_shipping_discount,
                        "can_view_settlement_discount": can_view_settlement_discount,
                        "can_view_promotion_rate": can_view_promotion_rate
                    }
                })
            else:
                # 非经销商用户：管理员返回原始费率，其他用户进行推广费率计算
                if user_role == 'admin':
                    # 管理员用户返回原始费率，不进行任何计算
                    return jsonify({"code": 0, "data": samples})
                else:
                    # 其他非经销商用户需要处理推广费率计算
                    samples = process_discount_rates_and_promotion(samples)
                    return jsonify({"code": 0, "data": samples})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取样书失败: {str(e)}"})
    finally:
        connection.close()

@common_bp.route('/get_publisher_directories', methods=['GET'])
def get_publisher_directories():
    """获取出版社和目录树结构"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})

    search = request.args.get('search', '')

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 获取所有出版社
            sql = """
                SELECT u.user_id, u.name, COUNT(sb.id) as sample_count
                FROM users u
                LEFT JOIN sample_books sb ON u.user_id = sb.publisher_id
                WHERE u.role = 'publisher'
                AND (
                    sb.name LIKE %s OR
                    sb.author LIKE %s OR
                    sb.isbn LIKE %s OR
                    u.name LIKE %s
                )
            """

            # 如果是出版社用户，只显示自己公司的出版社
            if session.get('role') == 'publisher':
                # 获取当前用户的公司ID
                cursor.execute("SELECT publisher_company_id FROM users WHERE user_id = %s", (session.get('user_id'),))
                user_company = cursor.fetchone()
                if user_company and user_company['publisher_company_id']:
                    sql += " AND u.publisher_company_id = %s"
                    sql += " GROUP BY u.user_id ORDER BY u.name"
                    cursor.execute(sql, (f'%{search}%', f'%{search}%', f'%{search}%', f'%{search}%', user_company['publisher_company_id']))
                else:
                    # 如果没有公司ID，不显示任何出版社
                    sql += " AND 1=0 GROUP BY u.user_id ORDER BY u.name"
                    cursor.execute(sql, (f'%{search}%', f'%{search}%', f'%{search}%', f'%{search}%'))
            else:
                sql += " GROUP BY u.user_id ORDER BY u.name"
                cursor.execute(sql, (f'%{search}%', f'%{search}%', f'%{search}%', f'%{search}%'))
            publishers = cursor.fetchall()

            result = []

            # 对每个出版社，获取其目录结构
            for publisher in publishers:
                publisher_data = {
                    "id": publisher['user_id'],
                    "name": publisher['name'],
                    "type": "publisher",
                    "sample_count": publisher['sample_count'],
                    "directories": []
                }

                # 获取出版社的顶级目录
                sql = """
                    SELECT d.id, d.name, COUNT(sb.id) as sample_count
                    FROM directories d
                    LEFT JOIN sample_books sb ON d.id = sb.parent_id
                    WHERE d.publisher_id = %s AND d.parent_id IS NULL
                    AND (
                        sb.name LIKE %s OR
                        sb.author LIKE %s OR
                        sb.isbn LIKE %s OR
                        %s = ''
                    )
                    GROUP BY d.id
                    ORDER BY d.name
                """
                cursor.execute(sql, (publisher['user_id'], f'%{search}%', f'%{search}%', f'%{search}%', search))
                directories = cursor.fetchall()

                # 递归获取子目录
                for directory in directories:
                    dir_data = {
                        "id": directory['id'],
                        "name": directory['name'],
                        "type": "directory",
                        "sample_count": directory['sample_count'],
                        "subdirectories": get_subdirectories(cursor, directory['id'], search)
                    }
                    publisher_data["directories"].append(dir_data)

                result.append(publisher_data)

            return jsonify({"code": 0, "data": result})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取目录失败: {str(e)}"})
    finally:
        connection.close()

# 递归获取子目录的辅助函数
def get_subdirectories(cursor, parent_id, search=''):
    sql = """
        SELECT d.id, d.name, COUNT(sb.id) as sample_count
        FROM directories d
        LEFT JOIN sample_books sb ON d.id = sb.parent_id
        WHERE d.parent_id = %s
        AND (
            sb.name LIKE %s OR
            sb.author LIKE %s OR
            sb.isbn LIKE %s OR
            %s = ''
        )
        GROUP BY d.id
        ORDER BY d.name
    """
    cursor.execute(sql, (parent_id, f'%{search}%', f'%{search}%', f'%{search}%', search))
    directories = cursor.fetchall()

    result = []
    for directory in directories:
        dir_data = {
            "id": directory['id'],
            "name": directory['name'],
            "type": "directory",
            "sample_count": directory['sample_count'],
            "subdirectories": get_subdirectories(cursor, directory['id'], search)
        }
        result.append(dir_data)

    return result

@common_bp.route('/get_samples_by_publisher', methods=['GET'])
def get_samples_by_publisher():
    """根据出版社ID获取样书"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})

    publisher_id = request.args.get('id')
    search = request.args.get('search', '')
    user_id = session.get('user_id')
    user_role = session.get('role')

    if not publisher_id:
        return jsonify({"code": 1, "message": "出版社ID为必填项"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 获取经销商组织信息
            organization_id = None
            if user_role == 'dealer':
                from app.users.dealer import get_dealer_organization_id
                organization_id = get_dealer_organization_id(cursor, user_id)

            sql = """
                SELECT sb.id, sb.name, sb.author, sb.isbn, sb.price, sb.award_info, sb.publisher_name,
                       sb.publisher_id, sb.level, sb.book_type, sb.national_regulation, sb.provincial_regulation,
                       sb.national_regulation_level_id, sb.provincial_regulation_level_id,
                       sb.material_type, sb.attachment_link, sb.publication_date,
                       sb.sample_download_url, sb.resource_download_url, sb.online_reading_url,
                       d.name as directory_name,
                       nrl.name as national_regulation_level_name,
                       prl.name as provincial_regulation_level_name,
                       (SELECT GROUP_CONCAT(bf.name SEPARATOR ', ')
                        FROM sample_book_features sbf
                        JOIN book_features bf ON sbf.feature_id = bf.id
                        WHERE sbf.sample_id = sb.id) as feature_name,
                       CASE
                           WHEN sb.cooperation_status = 1 AND sb.award_info IS NOT NULL THEN 1
                           WHEN sb.cooperation_status = 1 AND sb.award_info IS NULL THEN 2
                           WHEN sb.cooperation_status = 0 AND sb.award_info IS NOT NULL THEN 3
                           ELSE 4
                       END AS priority
            """

            # 如果是经销商用户，添加折扣率字段
            if user_role == 'dealer':
                sql += ", sb.shipping_discount, sb.settlement_discount, sb.promotion_rate"

            sql += """
                FROM sample_books sb
                JOIN users u ON sb.publisher_id = u.user_id
                LEFT JOIN directories d ON sb.parent_id = d.id
                LEFT JOIN national_regulation_levels nrl ON sb.national_regulation_level_id = nrl.id
                LEFT JOIN provincial_regulation_levels prl ON sb.provincial_regulation_level_id = prl.id
                WHERE sb.publisher_id = %s
                AND (
                    sb.name LIKE %s OR
                    sb.author LIKE %s OR
                    sb.isbn LIKE %s
                )
            """

            # 如果是出版社用户，只能查看自己公司的样书
            if user_role == 'publisher':
                # 获取当前用户的公司ID
                cursor.execute("SELECT publisher_company_id FROM users WHERE user_id = %s", (user_id,))
                user_company = cursor.fetchone()
                if user_company and user_company['publisher_company_id']:
                    sql += " AND u.publisher_company_id = %s ORDER BY sb.name"
                    cursor.execute(sql, (publisher_id, f'%{search}%', f'%{search}%', f'%{search}%', user_company['publisher_company_id']))
                else:
                    # 如果没有公司ID，不显示任何样书
                    sql += " AND 1=0 ORDER BY sb.name"
                    cursor.execute(sql, (publisher_id, f'%{search}%', f'%{search}%', f'%{search}%'))
            else:
                sql += " ORDER BY sb.name"
                cursor.execute(sql, (publisher_id, f'%{search}%', f'%{search}%', f'%{search}%'))
            samples = cursor.fetchall()

            # 格式化日期字段
            for sample in samples:
                if sample.get('publication_date'):
                    sample['publication_date'] = sample['publication_date'].strftime('%Y-%m-%d')

            # 如果是经销商用户，处理费率计算和权限控制
            if user_role == 'dealer':
                # 使用新的统一费率处理函数
                if organization_id:
                    samples = process_dealer_rates_for_samples(cursor, organization_id, samples)
                else:
                    # 如果没有组织ID，保持原始费率不变
                    pass

                # 检查经销商的费率查看权限
                from app.users.admin import check_user_permission
                can_view_shipping_discount = check_user_permission(user_id, 'view_shipping_discount')
                can_view_settlement_discount = check_user_permission(user_id, 'view_settlement_discount')
                can_view_promotion_rate = check_user_permission(user_id, 'view_promotion_rate')

                # 根据权限过滤费率信息
                for sample in samples:
                    if not can_view_shipping_discount:
                        sample.pop('shipping_discount', None)
                    if not can_view_settlement_discount:
                        sample.pop('settlement_discount', None)
                    if not can_view_promotion_rate:
                        sample.pop('promotion_rate', None)
                        sample.pop('promotion_rate_source', None)
                        sample.pop('promotion_rate_calculated', None)

                # 返回权限信息给前端
                return jsonify({
                    "code": 0,
                    "data": samples,
                    "permissions": {
                        "can_view_shipping_discount": can_view_shipping_discount,
                        "can_view_settlement_discount": can_view_settlement_discount,
                        "can_view_promotion_rate": can_view_promotion_rate
                    }
                })
            else:
                # 非经销商用户：管理员返回原始费率，其他用户进行推广费率计算
                if user_role == 'admin':
                    # 管理员用户返回原始费率，不进行任何计算
                    return jsonify({"code": 0, "data": samples})
                else:
                    # 其他非经销商用户需要处理推广费率计算（使用兼容性函数）
                    samples = process_discount_rates_and_promotion(samples)
                    return jsonify({"code": 0, "data": samples})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取样书失败: {str(e)}"})
    finally:
        connection.close()

@common_bp.route('/get_samples_by_directory', methods=['GET'])
def get_samples_by_directory():
    """根据目录ID获取样书"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})

    directory_id = request.args.get('id')
    search = request.args.get('search', '')
    user_id = session.get('user_id')
    user_role = session.get('role')

    if not directory_id:
        return jsonify({"code": 1, "message": "目录ID为必填项"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 获取经销商信息
            organization_id = None
            if user_role == 'dealer':
                from app.users.dealer import get_dealer_organization_id
                organization_id = get_dealer_organization_id(cursor, user_id)

            # 获取当前目录及其所有子目录的ID
            directory_ids = [directory_id]
            get_all_subdirectory_ids(cursor, directory_id, directory_ids)

            # 构建SQL的IN子句
            placeholders = ', '.join(['%s'] * len(directory_ids))

            sql = f"""
                SELECT sb.id, sb.name, sb.author, sb.isbn, sb.price, sb.award_info,
                       sb.publisher_id, sb.level, sb.book_type, sb.national_regulation, sb.provincial_regulation,
                       sb.national_regulation_level_id, sb.provincial_regulation_level_id,
                       sb.material_type, sb.attachment_link, sb.publication_date,
                       sb.sample_download_url, sb.resource_download_url, sb.online_reading_url,
                       u.name as publisher_name, d.name as directory_name,
                       nrl.name as national_regulation_level_name,
                       prl.name as provincial_regulation_level_name,
                       (SELECT GROUP_CONCAT(bf.name SEPARATOR ', ')
                        FROM sample_book_features sbf
                        JOIN book_features bf ON sbf.feature_id = bf.id
                        WHERE sbf.sample_id = sb.id) as feature_name,
                       CASE
                           WHEN sb.cooperation_status = 1 AND sb.award_info IS NOT NULL THEN 1
                           WHEN sb.cooperation_status = 1 AND sb.award_info IS NULL THEN 2
                           WHEN sb.cooperation_status = 0 AND sb.award_info IS NOT NULL THEN 3
                           ELSE 4
                       END AS priority
            """

            # 如果是经销商用户或管理员用户，添加折扣率字段
            if user_role in ['dealer', 'admin']:
                sql += ", sb.shipping_discount, sb.settlement_discount, sb.promotion_rate"

            sql += f"""
                FROM sample_books sb
                JOIN users u ON sb.publisher_id = u.user_id
                LEFT JOIN directories d ON sb.parent_id = d.id
                LEFT JOIN national_regulation_levels nrl ON sb.national_regulation_level_id = nrl.id
                LEFT JOIN provincial_regulation_levels prl ON sb.provincial_regulation_level_id = prl.id
                WHERE sb.parent_id IN ({placeholders})
                AND (
                    sb.name LIKE %s OR
                    sb.author LIKE %s OR
                    sb.isbn LIKE %s OR
                    %s = ''
                )
            """

            params = directory_ids + [f'%{search}%', f'%{search}%', f'%{search}%', search]

            # 如果是出版社用户，只能查看自己公司的样书
            if user_role == 'publisher':
                # 获取当前用户的公司ID
                cursor.execute("SELECT publisher_company_id FROM users WHERE user_id = %s", (user_id,))
                user_company = cursor.fetchone()
                if user_company and user_company['publisher_company_id']:
                    sql += " AND u.publisher_company_id = %s ORDER BY priority, sb.name"
                    params.append(user_company['publisher_company_id'])
                else:
                    # 如果没有公司ID，不显示任何样书
                    sql += " AND 1=0 ORDER BY priority, sb.name"
            else:
                sql += " ORDER BY priority, sb.name"

            cursor.execute(sql, params)
            samples = cursor.fetchall()

            # 格式化日期字段
            for sample in samples:
                if sample.get('publication_date'):
                    sample['publication_date'] = sample['publication_date'].strftime('%Y-%m-%d')

            # 如果是经销商用户，处理费率计算和权限控制
            if user_role == 'dealer':
                # 使用新的统一费率处理函数
                if organization_id:
                    samples = process_dealer_rates_for_samples(cursor, organization_id, samples)
                else:
                    # 如果没有组织ID，保持原始费率不变
                    pass

                # 检查经销商的费率查看权限
                from app.users.admin import check_user_permission
                can_view_shipping_discount = check_user_permission(user_id, 'view_shipping_discount')
                can_view_settlement_discount = check_user_permission(user_id, 'view_settlement_discount')
                can_view_promotion_rate = check_user_permission(user_id, 'view_promotion_rate')

                # 根据权限过滤费率信息
                for sample in samples:
                    if not can_view_shipping_discount:
                        sample.pop('shipping_discount', None)
                    if not can_view_settlement_discount:
                        sample.pop('settlement_discount', None)
                    if not can_view_promotion_rate:
                        sample.pop('promotion_rate', None)
                        sample.pop('promotion_rate_source', None)
                        sample.pop('promotion_rate_calculated', None)

                # 返回权限信息给前端
                return jsonify({
                    "code": 0,
                    "data": samples,
                    "permissions": {
                        "can_view_shipping_discount": can_view_shipping_discount,
                        "can_view_settlement_discount": can_view_settlement_discount,
                        "can_view_promotion_rate": can_view_promotion_rate
                    }
                })
            else:
                # 非经销商用户：管理员返回原始费率，其他用户进行推广费率计算
                if user_role == 'admin':
                    # 管理员用户返回原始费率，不进行任何计算
                    return jsonify({"code": 0, "data": samples})
                else:
                    # 其他非经销商用户需要处理推广费率计算
                    samples = process_discount_rates_and_promotion(samples)
                    return jsonify({"code": 0, "data": samples})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取样书失败: {str(e)}"})
    finally:
        connection.close()

# 递归获取所有子目录ID的辅助函数
def get_all_subdirectory_ids(cursor, parent_id, result_list):
    sql = "SELECT id FROM directories WHERE parent_id = %s"
    cursor.execute(sql, (parent_id,))
    subdirectories = cursor.fetchall()

    for subdir in subdirectories:
        subdir_id = subdir['id']
        result_list.append(subdir_id)
        get_all_subdirectory_ids(cursor, subdir_id, result_list)

@common_bp.route('/get_sample_detail', methods=['GET'])
def get_sample_detail():
    """获取样书详情"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})

    sample_id = request.args.get('id')
    user_id = session.get('user_id')
    user_role = session.get('role')

    if not sample_id:
        return jsonify({"code": 1, "message": "缺少样书ID参数"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 获取经销商组织信息
            organization_id = None
            if user_role == 'dealer':
                from app.users.dealer import get_dealer_organization_id
                organization_id = get_dealer_organization_id(cursor, user_id)

            sql = """
                SELECT sb.*, d.name as directory_name,
                       nrl.name as national_regulation_level_name,
                       prl.name as provincial_regulation_level_name,
                       (SELECT GROUP_CONCAT(bf.name SEPARATOR ', ')
                        FROM sample_book_features sbf
                        JOIN book_features bf ON sbf.feature_id = bf.id
                        WHERE sbf.sample_id = sb.id) as feature_name
                FROM sample_books sb
                LEFT JOIN directories d ON sb.parent_id = d.id
                LEFT JOIN users u ON sb.publisher_id = u.user_id
                LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id
                LEFT JOIN national_regulation_levels nrl ON sb.national_regulation_level_id = nrl.id
                LEFT JOIN provincial_regulation_levels prl ON sb.provincial_regulation_level_id = prl.id
                WHERE sb.id = %s
            """
            cursor.execute(sql, (sample_id,))
            sample = cursor.fetchone()

            if not sample:
                return jsonify({"code": 1, "message": "样书不存在"})

            # 格式化日期字段
            if sample.get('publication_date'):
                sample['publication_date'] = sample['publication_date'].strftime('%Y-%m-%d')

            # 如果是经销商用户，处理费率计算和权限控制
            if user_role == 'dealer' and sample:
                # 使用新的统一费率处理函数
                if organization_id:
                    sample = calculate_dealer_rates(cursor, organization_id, sample)
                else:
                    # 如果没有组织ID，保持原始费率不变
                    pass

                # 检查经销商的费率查看权限
                from app.users.admin import check_user_permission
                can_view_shipping_discount = check_user_permission(user_id, 'view_shipping_discount')
                can_view_settlement_discount = check_user_permission(user_id, 'view_settlement_discount')
                can_view_promotion_rate = check_user_permission(user_id, 'view_promotion_rate')

                # 根据权限过滤费率信息
                if not can_view_shipping_discount:
                    sample.pop('shipping_discount', None)
                if not can_view_settlement_discount:
                    sample.pop('settlement_discount', None)
                if not can_view_promotion_rate:
                    sample.pop('promotion_rate', None)
                    sample.pop('promotion_rate_source', None)
                    sample.pop('promotion_rate_calculated', None)

                # 返回权限信息给前端
                return jsonify({
                    "code": 0,
                    "data": sample,
                    "permissions": {
                        "can_view_shipping_discount": can_view_shipping_discount,
                        "can_view_settlement_discount": can_view_settlement_discount,
                        "can_view_promotion_rate": can_view_promotion_rate
                    }
                })
            elif sample:
                # 非经销商用户：管理员返回原始费率，其他用户进行推广费率计算
                if user_role == 'admin':
                    # 管理员用户返回原始费率，不进行任何计算
                    return jsonify({"code": 0, "data": sample})
                else:
                    # 其他非经销商用户需要处理推广费率计算
                    samples = process_discount_rates_and_promotion([sample])
                    sample = samples[0]
                    return jsonify({"code": 0, "data": sample})
            else:
                return jsonify({"code": 0, "data": sample})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取样书详情失败: {str(e)}"})
    finally:
        connection.close()

@common_bp.route('/filter_samples', methods=['GET'])
def filter_samples():
    """高级筛选样书"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})

    # 获取筛选参数
    search = request.args.get('search', '')
    user_id = session.get('user_id')
    user_role = session.get('role')

    # 解析JSON筛选条件
    try:
        levels = json.loads(request.args.get('levels', '[]'))
        types = json.loads(request.args.get('types', '[]'))
        ranks = json.loads(request.args.get('ranks', '[]'))
        national_levels = json.loads(request.args.get('national_levels', '[]'))
        provincial_levels = json.loads(request.args.get('provincial_levels', '[]'))
        publishers = json.loads(request.args.get('publishers', '[]'))
        features = json.loads(request.args.get('features', '[]'))
    except json.JSONDecodeError:
        return jsonify({"code": 1, "message": "筛选条件格式不正确"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 获取经销商组织信息
            organization_id = None
            if user_role == 'dealer':
                from app.users.dealer import get_dealer_organization_id
                organization_id = get_dealer_organization_id(cursor, user_id)

            # 构建基础SQL查询
            sql = """
                SELECT sb.id, sb.name, sb.author, sb.isbn, sb.price, sb.award_info,
                       sb.publisher_id, sb.level, sb.book_type, sb.national_regulation, sb.provincial_regulation,
                       sb.national_regulation_level_id, sb.provincial_regulation_level_id,
                       sb.material_type, sb.attachment_link, sb.publication_date,
                       sb.sample_download_url, sb.resource_download_url, sb.online_reading_url,
                       sb.publisher_name,
                       d.name as directory_name,
                       nrl.name as national_regulation_level_name,
                       prl.name as provincial_regulation_level_name,
                       (SELECT GROUP_CONCAT(bf.name SEPARATOR ', ')
                        FROM sample_book_features sbf
                        JOIN book_features bf ON sbf.feature_id = bf.id
                        WHERE sbf.sample_id = sb.id) as feature_name
            """

            # 如果是经销商用户，添加折扣率字段
            if user_role == 'dealer':
                sql += ", sb.shipping_discount, sb.settlement_discount, sb.promotion_rate"

            sql += """
                FROM sample_books sb
                LEFT JOIN directories d ON sb.parent_id = d.id
                LEFT JOIN users u ON sb.publisher_id = u.user_id
                LEFT JOIN publisher_companies pc ON u.publisher_company_id = pc.id
                LEFT JOIN national_regulation_levels nrl ON sb.national_regulation_level_id = nrl.id
                LEFT JOIN provincial_regulation_levels prl ON sb.provincial_regulation_level_id = prl.id
                WHERE 1=1
            """

            params = []

            # 添加搜索条件
            if search:
                sql += """ AND (
                    sb.name LIKE %s OR
                    sb.author LIKE %s OR
                    sb.isbn LIKE %s
                )"""
                params.extend([f'%{search}%', f'%{search}%', f'%{search}%'])

            # 添加学校层次筛选
            if levels and len(levels) > 0:
                placeholders = ', '.join(['%s'] * len(levels))
                sql += f" AND (sb.level IN ({placeholders}))"
                params.extend(levels)

            # 添加图书类型筛选
            if types and len(types) > 0:
                placeholders = ', '.join(['%s'] * len(types))
                sql += f" AND (sb.book_type IN ({placeholders}))"
                params.extend(types)

            # 添加规划级别筛选
            if ranks and len(ranks) > 0:
                rank_conditions = []
                for rank in ranks:
                    if rank == '国家规划':
                        rank_conditions.append("sb.national_regulation = 1")
                    elif rank == '省级规划':
                        rank_conditions.append("sb.provincial_regulation = 1")
                    elif rank == '普通教材':
                        rank_conditions.append("(sb.national_regulation = 0 AND sb.provincial_regulation = 0)")

                if rank_conditions:
                    sql += " AND (" + " OR ".join(rank_conditions) + ")"

            # 添加国家规划级别筛选
            if national_levels and len(national_levels) > 0:
                placeholders = ', '.join(['%s'] * len(national_levels))
                sql += f" AND (sb.national_regulation_level_id IN ({placeholders}))"
                params.extend(national_levels)

            # 添加省级规划级别筛选
            if provincial_levels and len(provincial_levels) > 0:
                placeholders = ', '.join(['%s'] * len(provincial_levels))
                sql += f" AND (sb.provincial_regulation_level_id IN ({placeholders}))"
                params.extend(provincial_levels)

            # 添加出版社筛选
            if publishers and len(publishers) > 0:
                placeholders = ', '.join(['%s'] * len(publishers))
                sql += f" AND sb.publisher_name IN ({placeholders})"
                params.extend(publishers)

            # 添加特色标签筛选
            if features and len(features) > 0:
                placeholders = ', '.join(['%s'] * len(features))
                sql += f" AND EXISTS (SELECT 1 FROM sample_book_features sbf WHERE sbf.sample_id = sb.id AND sbf.feature_id IN ({placeholders}))"
                params.extend(features)

            # 如果是出版社用户，只能查看自己公司的样书
            if user_role == 'publisher':
                # 获取当前用户的公司ID
                cursor.execute("SELECT publisher_company_id FROM users WHERE user_id = %s", (user_id,))
                user_company = cursor.fetchone()
                if user_company and user_company['publisher_company_id']:
                    sql += " AND u.publisher_company_id = %s"
                    params.append(user_company['publisher_company_id'])
                else:
                    # 如果没有公司ID，不显示任何样书
                    sql += " AND 1=0"

            # 添加排序
            sql += " ORDER BY sb.name"

            # 执行查询
            cursor.execute(sql, params)
            samples = cursor.fetchall()

            # 如果是经销商用户，处理费率计算和权限控制
            if user_role == 'dealer':
                # 使用新的统一费率处理函数
                if organization_id:
                    samples = process_dealer_rates_for_samples(cursor, organization_id, samples)
                else:
                    # 如果没有组织ID，保持原始费率不变
                    pass

                # 检查经销商的费率查看权限
                from app.users.admin import check_user_permission
                can_view_shipping_discount = check_user_permission(user_id, 'view_shipping_discount')
                can_view_settlement_discount = check_user_permission(user_id, 'view_settlement_discount')
                can_view_promotion_rate = check_user_permission(user_id, 'view_promotion_rate')

                # 根据权限过滤费率信息
                for sample in samples:
                    if not can_view_shipping_discount:
                        sample.pop('shipping_discount', None)
                    if not can_view_settlement_discount:
                        sample.pop('settlement_discount', None)
                    if not can_view_promotion_rate:
                        sample.pop('promotion_rate', None)
                        sample.pop('promotion_rate_source', None)
                        sample.pop('promotion_rate_calculated', None)

                # 返回权限信息给前端
                return jsonify({
                    "code": 0,
                    "data": samples,
                    "permissions": {
                        "can_view_shipping_discount": can_view_shipping_discount,
                        "can_view_settlement_discount": can_view_settlement_discount,
                        "can_view_promotion_rate": can_view_promotion_rate
                    }
                })
            else:
                # 非经销商用户：管理员返回原始费率，其他用户进行推广费率计算
                if user_role == 'admin':
                    # 管理员用户返回原始费率，不进行任何计算
                    return jsonify({"code": 0, "data": samples})
                else:
                    # 其他非经销商用户需要处理推广费率计算
                    samples = process_discount_rates_and_promotion(samples)
                    return jsonify({"code": 0, "data": samples})
    except Exception as e:
        return jsonify({"code": 1, "message": f"筛选样书失败: {str(e)}"})
    finally:
        connection.close()

@common_bp.route('/get_publishers', methods=['GET'])
def get_publishers():
    """获取出版社列表"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = """
                SELECT DISTINCT publisher_name as name
                FROM sample_books
                WHERE publisher_name IS NOT NULL AND publisher_name != ''
                ORDER BY publisher_name
            """
            cursor.execute(sql)
            publishers = cursor.fetchall()

            return jsonify({"code": 0, "data": publishers})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取出版社列表失败: {str(e)}"})
    finally:
        connection.close()

@common_bp.route('/get_national_regulation_levels', methods=['GET'])
def get_national_regulation_levels():
    """获取国家规划级别"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 查询国家规划级别
            sql = """
                SELECT id, name, description, created_at
                FROM national_regulation_levels
                ORDER BY id
            """
            cursor.execute(sql)
            levels = cursor.fetchall()

            return jsonify({"code": 0, "data": levels})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取国家规划级别失败: {str(e)}"})
    finally:
        connection.close()

@common_bp.route('/get_provincial_regulation_levels', methods=['GET'])
def get_provincial_regulation_levels():
    """获取省级规划级别"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 查询省级规划级别
            sql = """
                SELECT id, name, province, description, created_at
                FROM provincial_regulation_levels
                ORDER BY id
            """
            cursor.execute(sql)
            levels = cursor.fetchall()

            return jsonify({"code": 0, "data": levels})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取省级规划级别失败: {str(e)}"})
    finally:
        connection.close()

@common_bp.route('/get_book_features', methods=['GET'])
def get_book_features():
    """获取所有特色选项"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 查询特色选项
            sql = """
                SELECT id, name, description, created_at
                FROM book_features
                ORDER BY id
            """
            cursor.execute(sql)
            features = cursor.fetchall()

            return jsonify({"code": 0, "data": features})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取特色选项失败: {str(e)}"})
    finally:
        connection.close()

@common_bp.route('/get_book_levels', methods=['GET'])
def get_book_levels():
    """获取学校层次列表"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = """
                SELECT id, name
                FROM book_levels
                ORDER BY name
            """
            cursor.execute(sql)
            results = cursor.fetchall()

            return jsonify({"code": 0, "data": results})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取学校层次失败: {str(e)}"})
    finally:
        connection.close()

@common_bp.route('/get_book_types', methods=['GET'])
def get_book_types():
    """获取图书类型列表"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = """
                SELECT id, name
                FROM book_types
            """
            cursor.execute(sql)
            results = cursor.fetchall()

            return jsonify({"code": 0, "data": results})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取图书类型失败: {str(e)}"})
    finally:
        connection.close()

@common_bp.route('/get_color_systems', methods=['GET'])
def get_color_systems():
    """获取色系列表"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = """
                SELECT id, name, description
                FROM color_systems
                ORDER BY created_at DESC
            """
            cursor.execute(sql)
            color_systems = cursor.fetchall()

            return jsonify({"code": 0, "message": "获取成功", "data": color_systems})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取色系列表失败: {str(e)}"})
    finally:
        connection.close()

@common_bp.route('/get_material_types', methods=['GET'])
def get_material_types():
    """获取教材类型列表"""
    if 'user_id' not in session:
        return jsonify({"code": 1, "message": "未登录"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = """
                SELECT id, name, description
                FROM material_types
                ORDER BY created_at DESC
            """
            cursor.execute(sql)
            material_types = cursor.fetchall()

            return jsonify({"code": 0, "message": "获取成功", "data": material_types})
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取教材类型列表失败: {str(e)}"})
    finally:
        connection.close()


# 注册验证码相关接口
@common_bp.route('/send_registration_code', methods=['POST'])
def send_registration_code():
    """
    发送注册验证码
    请求数据:
        email: 用户邮箱
    返回:
        发送结果
    """
    data = request.get_json() if request.is_json else request.form.to_dict()
    email = data.get('email')

    if not email:
        return jsonify({"code": 1, "message": "请提供邮箱地址"})

    # 验证邮箱格式
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(email_pattern, email):
        return jsonify({"code": 1, "message": "邮箱格式不正确"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 检查邮箱是否已被注册
            sql = "SELECT user_id FROM users WHERE email = %s"
            cursor.execute(sql, (email,))
            user = cursor.fetchone()

            if user:
                return jsonify({"code": 1, "message": "该邮箱已被注册"})

            # 检查是否在短时间内重复发送（防止频繁发送）
            check_sql = """
            SELECT id FROM registration_verification_codes
            WHERE email = %s AND created_at > DATE_SUB(NOW(), INTERVAL 1 MINUTE)
            """
            cursor.execute(check_sql, (email,))
            recent_code = cursor.fetchone()

            if recent_code:
                return jsonify({"code": 1, "message": "验证码发送过于频繁，请稍后再试"})

            # 生成6位随机验证码
            verification_code = ''.join(random.choices(string.digits, k=6))
            expires_at = datetime.datetime.now() + datetime.timedelta(minutes=10)

            # 先删除该邮箱的旧验证码
            delete_sql = "DELETE FROM registration_verification_codes WHERE email = %s"
            cursor.execute(delete_sql, (email,))

            # 保存验证码到数据库
            insert_sql = """
            INSERT INTO registration_verification_codes (email, code, created_at, expires_at)
            VALUES (%s, %s, %s, %s)
            """
            cursor.execute(insert_sql, (email, verification_code,
                                      datetime.datetime.now(), expires_at))
            connection.commit()

            # 发送验证码到邮箱
            from app.services.site_config_service import get_site_name
            site_name = get_site_name()
            email_subject = f"【{site_name}】注册验证码"
            email_content = f"""尊敬的用户，

您正在注册系统账号，验证码为：{verification_code}

验证码有效期为10分钟，请及时使用。

如果不是您本人操作，请忽略此邮件。

此邮件由系统自动发送，请勿回复。"""

            from app.services.email_service import send_notification_email
            result = send_notification_email(email, email_subject, email_content)

            if result.get('success'):
                return jsonify({"code": 0, "message": "验证码已发送到您的邮箱"})
            else:
                return jsonify({"code": 1, "message": "验证码发送失败，请重试"})

    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"发送验证码失败: {str(e)}"})
    finally:
        connection.close()


# 密码找回相关接口
@common_bp.route('/send_reset_code', methods=['POST'])
def send_reset_code():
    """
    发送密码重置验证码
    请求数据:
        email: 用户邮箱
    返回:
        发送结果
    """
    data = request.get_json()
    email = data.get('email')

    if not email:
        return jsonify({"code": 1, "message": "请提供邮箱地址"})

    # 验证邮箱格式
    email_pattern = r'^[\w\.-]+@([\w\-]+\.)+[A-Za-z]{2,}$'
    if not re.match(email_pattern, email):
        return jsonify({"code": 1, "message": "邮箱格式不正确"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 检查邮箱是否存在
            sql = "SELECT user_id, username FROM users WHERE email = %s"
            cursor.execute(sql, (email,))
            user = cursor.fetchone()

            if not user:
                return jsonify({"code": 1, "message": "该邮箱未注册"})

            # 生成6位随机验证码
            verification_code = ''.join(random.choices(string.digits, k=6))
            expires_at = datetime.datetime.now() + datetime.timedelta(minutes=10)

            # 先删除该邮箱的旧验证码
            delete_sql = "DELETE FROM password_reset_codes WHERE email = %s"
            cursor.execute(delete_sql, (email,))

            # 保存验证码到数据库
            insert_sql = """
            INSERT INTO password_reset_codes (email, code, user_id, created_at, expires_at)
            VALUES (%s, %s, %s, %s, %s)
            """
            cursor.execute(insert_sql, (email, verification_code, user['user_id'],
                                      datetime.datetime.now(), expires_at))
            connection.commit()

            # 发送验证码到邮箱
            from app.services.site_config_service import get_site_name
            site_name = get_site_name()
            username = user['username']
            email_subject = f"【{site_name}】密码重置验证码"
            email_content = f"尊敬的用户 {username}，\n\n您正在进行密码重置操作，验证码为：{verification_code}，有效期10分钟。\n\n如果不是您本人操作，请忽略此邮件。\n\n此邮件由系统自动发送，请勿回复。"

            send_notification_email(email, email_subject, email_content)

            return jsonify({"code": 0, "message": "验证码已发送到您的邮箱"})

    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"发送验证码失败: {str(e)}"})
    finally:
        connection.close()


@common_bp.route('/verify_reset_code', methods=['POST'])
def verify_reset_code():
    """
    验证密码重置验证码
    请求数据:
        email: 用户邮箱
        code: 验证码
    返回:
        验证结果
    """
    data = request.get_json()
    email = data.get('email')
    code = data.get('code')

    if not email or not code:
        return jsonify({"code": 1, "message": "请提供邮箱和验证码"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 查询验证码是否有效
            sql = """
            SELECT id, code, expires_at
            FROM password_reset_codes
            WHERE email = %s
            ORDER BY created_at DESC LIMIT 1
            """
            cursor.execute(sql, (email,))
            code_record = cursor.fetchone()

            if not code_record:
                return jsonify({"code": 1, "message": "验证码不存在或已过期，请重新获取"})

            # 验证码过期检查
            if datetime.datetime.now() > code_record['expires_at']:
                return jsonify({"code": 1, "message": "验证码已过期，请重新获取"})

            # 验证码是否正确
            if code != code_record['code']:
                return jsonify({"code": 1, "message": "验证码不正确"})

            return jsonify({"code": 0, "message": "验证码验证成功"})

    except Exception as e:
        return jsonify({"code": 1, "message": f"验证失败: {str(e)}"})
    finally:
        connection.close()


@common_bp.route('/reset_password', methods=['POST'])
def reset_password():
    """
    重置密码
    请求数据:
        email: 用户邮箱
        code: 验证码
        new_password: 新密码
    返回:
        重置结果
    """
    data = request.get_json()
    email = data.get('email')
    code = data.get('code')
    new_password = data.get('new_password')

    if not email or not code or not new_password:
        return jsonify({"code": 1, "message": "请提供完整信息"})

    # 密码长度检查
    if len(new_password) < 6:
        return jsonify({"code": 1, "message": "密码长度不能少于6个字符"})

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 再次验证验证码
            sql = """
            SELECT id, code, user_id, expires_at
            FROM password_reset_codes
            WHERE email = %s
            ORDER BY created_at DESC LIMIT 1
            """
            cursor.execute(sql, (email,))
            code_record = cursor.fetchone()

            if not code_record:
                return jsonify({"code": 1, "message": "验证码不存在或已过期，请重新获取"})

            # 验证码过期检查
            if datetime.datetime.now() > code_record['expires_at']:
                return jsonify({"code": 1, "message": "验证码已过期，请重新获取"})

            # 验证码是否正确
            if code != code_record['code']:
                return jsonify({"code": 1, "message": "验证码不正确"})

            # 更新密码
            user_id = code_record['user_id']
            password_hash = generate_password_hash(new_password)

            update_sql = "UPDATE users SET password = %s WHERE user_id = %s"
            cursor.execute(update_sql, (password_hash, user_id))

            # 删除已使用的验证码
            delete_sql = "DELETE FROM password_reset_codes WHERE email = %s"
            cursor.execute(delete_sql, (email,))

            connection.commit()

            return jsonify({"code": 0, "message": "密码重置成功"})

    except Exception as e:
        connection.rollback()
        return jsonify({"code": 1, "message": f"密码重置失败: {str(e)}"})
    finally:
        connection.close()

@common_bp.route('/get_site_info', methods=['GET'])
def get_site_info():
    """
    获取网站信息
    请求参数:
        role: 用户角色 (default, admin, teacher, publisher, dealer)
        url: URL参数，用于获取组织配置
    返回:
        网站信息 (网站名称、logo)
    """
    connection = None
    try:
        role = request.args.get('role', 'default')
        url_param = request.args.get('url', '')

        # 验证角色是否有效
        valid_roles = ['default', 'admin', 'teacher', 'publisher', 'dealer']
        if role not in valid_roles:
            role = 'default'

        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 如果有URL参数，优先根据URL参数查询配置
        if url_param:
            cursor.execute("""
                SELECT site_name, logo_url, user_role, organization_type
                FROM site_settings
                WHERE login_url = %s AND is_active = 1
            """, (url_param,))
            url_config = cursor.fetchone()

            if url_config:
                return jsonify({
                    "code": 0,
                    "message": "获取成功",
                    "data": {
                        "site_name": url_config['site_name'],
                        "logo_url": url_config['logo_url'] if url_config['logo_url'] else None
                    }
                })

        # 首先获取默认配置
        cursor.execute("""
            SELECT site_name, logo_url
            FROM site_settings
            WHERE user_role = 'default' AND is_active = 1
        """)
        default_config = cursor.fetchone()

        # 如果没有默认配置，返回系统默认值
        if not default_config:
            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": {
                    "site_name": "系统",
                    "logo_url": None
                }
            })

        # 如果请求的是默认角色，直接返回默认配置
        if role == 'default':
            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": {
                    "site_name": default_config['site_name'],
                    "logo_url": default_config['logo_url'] if default_config['logo_url'] else None
                }
            })

        # 获取指定角色的配置
        cursor.execute("""
            SELECT site_name, logo_url, is_active
            FROM site_settings
            WHERE user_role = %s
        """, (role,))
        role_config = cursor.fetchone()

        # 如果角色配置不存在或未启用，返回默认配置
        if not role_config or not role_config['is_active']:
            return jsonify({
                "code": 0,
                "message": "获取成功",
                "data": {
                    "site_name": default_config['site_name'],
                    "logo_url": default_config['logo_url'] if default_config['logo_url'] else None
                }
            })

        # 合并配置：角色配置覆盖默认配置
        result = {
            "site_name": role_config['site_name'] if role_config['site_name'] else default_config['site_name'],
            "logo_url": role_config['logo_url'] if role_config['logo_url'] else (default_config['logo_url'] if default_config['logo_url'] else None)
        }

        return jsonify({
            "code": 0,
            "message": "获取成功",
            "data": result
        })

    except Exception as e:
        return jsonify({"code": 1, "message": f"获取网站信息失败: {str(e)}"})
    finally:
        if connection:
            connection.close()

@common_bp.route('/upload/image', methods=['POST'])
def upload_image():
    """
    图片上传API - 用于富文本编辑器
    支持格式: JPG, JPEG, PNG, GIF, WebP
    最大大小: 5MB
    """
    if 'user_id' not in session:
        return jsonify({"success": False, "message": "用户未登录"})

    try:
        if 'file' not in request.files:
            return jsonify({"success": False, "message": "未选择文件"})

        file = request.files['file']
        if file.filename == '':
            return jsonify({"success": False, "message": "未选择文件"})

        # 定义允许的文件扩展名
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'webp'}

        # 检查文件大小（5MB限制）
        file.seek(0, 2)  # 移动到文件末尾
        file_size = file.tell()
        file.seek(0)  # 重置到文件开头

        if file_size > 5 * 1024 * 1024:  # 5MB
            return jsonify({"success": False, "message": "文件大小不能超过5MB"})

        # 生成唯一文件名
        import uuid
        import os

        # 先从原始文件名获取扩展名，避免secure_filename移除汉字后丢失扩展名
        original_filename = file.filename
        if '.' in original_filename:
            file_ext = original_filename.rsplit('.', 1)[1].lower()
        else:
            return jsonify({"success": False, "message": "文件名必须包含扩展名"})

        # 验证扩展名是否在允许的列表中
        if file_ext not in allowed_extensions:
            return jsonify({"success": False, "message": "不支持的文件格式，请上传 PNG、JPG、JPEG、GIF 或 WebP 格式的图片"})

        unique_filename = f"rich_text_{uuid.uuid4().hex}.{file_ext}"

        # 确保上传目录存在
        upload_dir = os.path.join('app', 'static', 'upload', 'rich_text')
        os.makedirs(upload_dir, exist_ok=True)

        # 保存文件
        file_path = os.path.join(upload_dir, unique_filename)
        file.save(file_path)

        # 返回相对路径
        relative_path = f"/static/upload/rich_text/{unique_filename}"

        return jsonify({
            "success": True,
            "message": "图片上传成功",
            "data": {
                "url": relative_path
            }
        })

    except Exception as e:
        return jsonify({"success": False, "message": f"图片上传失败: {str(e)}"})

@common_bp.route('/check_user_session', methods=['GET'])
def check_user_session():
    """检查用户session信息"""
    try:
        if 'user_id' not in session:
            return jsonify({"code": 1, "message": "用户未登录"})

        user_id = session.get('user_id')
        user_role = session.get('role')
        username = session.get('username')

        return jsonify({
            "code": 0,
            "message": "获取成功",
            "data": {
                "user_id": user_id,
                "role": user_role,
                "username": username,
                "has_permission": user_role in ['publisher', 'dealer']
            }
        })
    except Exception as e:
        return jsonify({"code": 1, "message": f"检查session失败: {str(e)}"})


@common_bp.route('/validate_invitation_code', methods=['POST'])
def validate_invitation_code():
    """
    验证邀请码
    请求数据:
        invitation_code: 邀请码
    返回:
        验证结果和邀请人信息
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"code": 1, "message": "请求数据为空"})

        invitation_code = data.get('invitation_code', '').strip()
        if not invitation_code:
            return jsonify({"code": 1, "message": "请输入邀请码"})

        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 查询邀请码是否存在
        query = """
        SELECT ic.id, ic.inviter_id, ic.code, ic.created_at,
               u.name as inviter_name, u.dealer_company_id,
               dc.name as company_name,
               dcp.can_invite_users
        FROM invitation_codes ic
        JOIN users u ON ic.inviter_id = u.user_id
        LEFT JOIN dealer_companies dc ON u.dealer_company_id = dc.id
        LEFT JOIN dealer_company_permissions dcp ON dc.id = dcp.company_id
        WHERE ic.code = %s AND u.role = 'dealer'
        """
        cursor.execute(query, (invitation_code,))
        invitation = cursor.fetchone()

        if not invitation:
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "邀请码不存在或无效"})

        # 检查邀请人是否有邀请权限
        if not invitation.get('can_invite_users'):
            cursor.close()
            connection.close()
            return jsonify({"code": 1, "message": "该邀请码的创建者没有邀请权限"})

        cursor.close()
        connection.close()

        return jsonify({
            "code": 0,
            "message": "邀请码验证成功",
            "data": {
                "invitation_id": invitation['id'],
                "inviter_name": invitation['inviter_name'],
                "company_name": invitation['company_name'] or '未知公司',
                "created_at": invitation['created_at'].strftime('%Y-%m-%d') if invitation['created_at'] else ''
            }
        })

    except Exception as e:
        return jsonify({"code": 1, "message": f"验证邀请码失败: {str(e)}"})


@common_bp.route('/get_teacher_register_url', methods=['GET'])
def get_teacher_register_url():
    """
    获取教师注册的URL参数
    返回:
        教师注册的URL参数
    """
    try:
        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 查询教师角色的登录URL配置
        query = """
        SELECT login_url
        FROM site_settings
        WHERE user_role = 'teacher' AND is_active = 1
        """
        cursor.execute(query)
        result = cursor.fetchone()

        cursor.close()
        connection.close()

        if not result or not result['login_url']:
            return jsonify({"code": 1, "message": "未找到教师注册配置"})

        return jsonify({
            "code": 0,
            "message": "获取成功",
            "data": {
                "url_param": result['login_url']
            }
        })

    except Exception as e:
        return jsonify({"code": 1, "message": f"获取教师注册URL失败: {str(e)}"})