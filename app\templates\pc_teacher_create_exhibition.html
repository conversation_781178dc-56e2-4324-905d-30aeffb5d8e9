<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建书展活动 - 教师中心</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <link href="/static/css/quill2.snow.css" rel="stylesheet">
    <script src="/static/jquery.js"></script>
    <script src="/static/js/quill2.js"></script>
    <script defer src="/static/js/alpine.min.js"></script>
    <style>
        /* 富文本编辑器样式 */
        .ql-editor {
            min-height: 120px;
            font-size: 14px;
            line-height: 1.6;
        }

        .ql-editor.ql-blank::before {
            opacity: var(--placeholder-opacity, 1);
            transition: opacity 0.3s ease;
        }

        .ql-toolbar {
            border-top: 1px solid #d1d5db;
            border-left: 1px solid #d1d5db;
            border-right: 1px solid #d1d5db;
            border-top-left-radius: 0.75rem;
            border-top-right-radius: 0.75rem;
        }

        .ql-container {
            border-bottom: 1px solid #d1d5db;
            border-left: 1px solid #d1d5db;
            border-right: 1px solid #d1d5db;
            border-bottom-left-radius: 0.75rem;
            border-bottom-right-radius: 0.75rem;
        }

        .ql-editor:focus {
            outline: none;
        }

        .quill-wrapper:focus-within .ql-toolbar,
        .quill-wrapper:focus-within .ql-container {
            border-color: #3b82f6;
            box-shadow: 0 0 0 1px #3b82f6;
        }

        /* 图片默认样式 */
        .ql-editor img {
            max-width: 33%;
            height: auto;
            width: 33%;
            display: block;
            margin: 0.5em auto;
        }

        /* 全屏按钮样式 */
        .expand-button {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            width: 28px;
            height: 28px;
            background: transparent;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
            transition: background-color 0.15s ease;
        }

        .expand-button:hover {
            background-color: #e6e6e6;
        }

        .expand-button:active {
            background-color: #ccc;
        }

        .expand-button i {
            color: #444;
            font-size: 11px;
            line-height: 1;
        }

        .quill-wrapper {
            position: relative;
        }

        /* 全屏编辑器样式 */
        .fullscreen-editor-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            z-index: 99999;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        /* 确保Alpine.js的x-show指令正确工作 */
        [x-cloak] { display: none !important; }

        .fullscreen-editor-container {
            background: white;
            border-radius: 1rem;
            width: 100%;
            max-width: 1200px;
            height: 80vh;
            display: flex;
            flex-direction: column;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .fullscreen-editor-header {
            padding: 1.5rem 2rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top-left-radius: 1rem;
            border-top-right-radius: 1rem;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        .fullscreen-editor-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .fullscreen-editor .ql-toolbar {
            border-radius: 0;
            border-left: none;
            border-right: none;
            border-top: none;
        }

        .fullscreen-editor .ql-container {
            flex: 1;
            border-radius: 0;
            border-left: none;
            border-right: none;
            border-bottom: none;
        }

        .fullscreen-editor .ql-editor {
            min-height: auto;
            height: 100%;
            font-size: 16px;
            line-height: 1.6;
            padding: 1.5rem;
        }

        /* 渐变按钮样式 */
        .btn-gradient-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }

        .btn-gradient-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }

        .btn-gradient-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            transition: all 0.3s ease;
        }

        .btn-gradient-success:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }

        /* 消息提示样式 */
        .message-toast {
            pointer-events: auto;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
            min-width: 250px;
        }

        .animate-fadeIn {
            animation: fadeIn 0.3s ease-in;
        }

        .animate-fadeOut {
            animation: fadeOut 0.3s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeOut {
            from { opacity: 1; transform: translateY(0); }
            to { opacity: 0; transform: translateY(-10px); }
        }

        /* 必填字段标记 */
        .required-field::after {
            content: '*';
            color: #ef4444;
            margin-left: 0.25rem;
        }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
    <!-- 消息提示容器 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-[9999] flex flex-col items-end space-y-2"></div>

    <div class="container mx-auto px-4 py-6" x-data="exhibitionManager()" x-init="initialize()">
        <!-- 页面头部 -->
        <div class="max-w-4xl mx-auto bg-white border-b border-gray-100 mb-6">
            <div class="px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <button onclick="history.back()"
                                class="flex items-center px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all rounded-lg border border-gray-200">
                            <i class="fas fa-arrow-left mr-2"></i>
                            <span>返回</span>
                        </button>
                        <div class="border-l border-gray-200 pl-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-calendar-plus text-blue-600"></i>
                                </div>
                                <div>
                                    <h1 class="text-lg font-semibold text-gray-900" x-text="isEditMode ? '编辑书展活动' : '创建书展活动'"></h1>
                                    <p class="text-sm text-gray-500">填写书展活动的详细信息，邀请出版社参与</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="hidden md:flex items-center space-x-4">
                        <div class="text-right">
                            <p class="text-xs text-gray-500">当前步骤</p>
                            <p class="text-sm font-medium text-gray-900">信息填写</p>
                        </div>
                        <div class="w-8 h-8 bg-gray-50 rounded-lg flex items-center justify-center">
                            <i class="fas fa-edit text-gray-600"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="max-w-4xl mx-auto">
            <form @submit.prevent="submitForm()" class="space-y-6">
                <!-- 基本信息卡片 -->
                <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-slate-200 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">基本信息</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="md:col-span-2">
                            <label for="title" class="block text-sm font-medium text-gray-700 mb-2 required-field">书展主题</label>
                            <input type="text" 
                                   id="title" 
                                   x-model="form.title"
                                   placeholder="例如：25秋肇庆学院教材巡展"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                   required>
                        </div>
                        
                        <div>
                            <label for="school" class="block text-sm font-medium text-gray-700 mb-2">发起学校</label>
                            <input type="text" 
                                   id="school" 
                                   x-model="form.school"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl bg-gray-50 focus:outline-none"
                                   readonly>
                        </div>

                        <div>
                            <label for="logoUpload" class="block text-sm font-medium text-gray-700 mb-2">活动Logo</label>
                            <div class="border-2 border-dashed border-gray-300 rounded-xl p-4 text-center hover:border-blue-400 transition-colors bg-gray-50">
                                <div x-show="!form.logoPreview">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" aria-hidden="true" fill="currentColor" class="w-8 h-8 mx-auto mb-3 text-gray-400">
                                        <path fill-rule="evenodd" d="M1.5 6a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 19.5 6v12a2.25 2.25 0 0 1-2.25 2.25H3.75A2.25 2.25 0 0 1 1.5 18V6ZM3 16.06V18c0 .414.336.75.75.75h16.5A.75.75 0 0 0 21 18v-1.94l-2.69-2.689a1.5 1.5 0 0 0-2.12 0l-.88.879.97.97a.75.75 0 1 1-1.06 1.06l-5.16-5.159a1.5 1.5 0 0 0-2.12 0L3 16.061Zm10.125-7.81a1.125 1.125 0 1 1 2.25 0 1.125 1.125 0 0 1-2.25 0Z" clip-rule="evenodd"/>
                                    </svg>
                                    <label for="logoUpload" class="cursor-pointer inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm">
                                        <input id="logoUpload" type="file" accept=".jpg,.jpeg,.png,.webp" class="sr-only" @change="handleLogoUpload($event)">
                                        <i class="fas fa-upload mr-2"></i>选择图片
                                    </label>
                                    <p class="text-xs text-gray-500 mt-2">支持 JPG, PNG, WebP 格式 - 最大 5MB</p>
                                </div>
                                <div x-show="form.logoPreview" class="relative">
                                    <img :src="form.logoPreview" alt="Logo预览" class="h-20 object-contain mx-auto">
                                    <button type="button" @click="removeLogo()" class="absolute top-0 right-0 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 书展介绍卡片 -->
                <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-slate-200 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">书展介绍</h2>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">详细介绍</label>
                        <div class="quill-wrapper">
                            <!-- 全屏编辑按钮 -->
                            <button type="button"
                                    class="expand-button"
                                    @click="openFullscreenEditor()"
                                    title="全屏编辑">
                                <i class="fas fa-expand text-slate-600"></i>
                            </button>
                            <!-- 编辑器容器 -->
                            <div id="description-editor" class="bg-white"></div>
                        </div>
                        <p class="text-xs text-gray-500 mt-2">支持富文本格式，可插入图片，点击右上角按钮可全屏编辑</p>
                    </div>
                </div>

                <!-- 时间信息卡片 -->
                <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-slate-200 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">时间安排</h2>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label for="startTime" class="block text-sm font-medium text-gray-700 mb-2 required-field">开始时间</label>
                            <input type="datetime-local"
                                   id="startTime"
                                   x-model="form.startTime"
                                   @change="checkTimeAdvance()"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                   required>
                            <!-- 时间提醒 -->
                            <div x-show="showTimeWarning"
                                 x-transition:enter="transition ease-out duration-200"
                                 x-transition:enter-start="opacity-0 transform scale-95"
                                 x-transition:enter-end="opacity-100 transform scale-100"
                                 x-transition:leave="transition ease-in duration-150"
                                 x-transition:leave-start="opacity-100 transform scale-100"
                                 x-transition:leave-end="opacity-0 transform scale-95"
                                 class="mt-2 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                                <div class="flex items-start space-x-2">
                                    <i class="fas fa-exclamation-triangle text-amber-600 mt-0.5"></i>
                                    <div class="text-sm text-amber-800">
                                        <p class="font-medium">时间提醒</p>
                                        <p>建议提前15天发起书展，方便协办方协调参展社。</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <label for="endTime" class="block text-sm font-medium text-gray-700 mb-2 required-field">结束时间</label>
                            <input type="datetime-local"
                                   id="endTime"
                                   x-model="form.endTime"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                   required>
                        </div>
                        <div>
                            <label for="registrationDeadline" class="block text-sm font-medium text-gray-700 mb-2 required-field">报名截止时间</label>
                            <input type="datetime-local"
                                   id="registrationDeadline"
                                   x-model="form.registrationDeadline"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                   required>
                        </div>
                    </div>
                </div>

                <!-- 地点信息卡片 -->
                <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-slate-200 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">地点信息</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="location" class="block text-sm font-medium text-gray-700 mb-2 required-field">具体地点</label>
                            <input type="text"
                                   id="location"
                                   x-model="form.location"
                                   placeholder="例如：第一教学楼108室"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                   required>
                        </div>
                        <div>
                            <label for="schoolAddress" class="block text-sm font-medium text-gray-700 mb-2 required-field">学校地址</label>
                            <input type="text"
                                   id="schoolAddress"
                                   x-model="form.schoolAddress"
                                   placeholder="例如：广州市天河区中山大道西2号"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                   required>
                        </div>
                    </div>
                </div>

                <!-- 进校报备卡片 -->
                <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-slate-200 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">进校报备</h2>
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <input type="checkbox"
                                   id="requiresRegistration"
                                   x-model="form.requiresRegistration"
                                   class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                            <label for="requiresRegistration" class="ml-2 text-sm font-medium text-gray-700">是否需要进校报备</label>
                        </div>

                        <div x-show="form.requiresRegistration" x-transition class="space-y-4">
                            <div>
                                <label for="registrationRequirements" class="block text-sm font-medium text-gray-700 mb-2">报备要求</label>
                                <textarea id="registrationRequirements"
                                          x-model="form.registrationRequirements"
                                          rows="3"
                                          placeholder="请描述进校报备的具体要求..."
                                          class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"></textarea>
                            </div>

                            <div>
                                <label for="qrcodeUpload" class="block text-sm font-medium text-gray-700 mb-2">报备二维码</label>
                                <div class="border-2 border-dashed border-gray-300 rounded-xl p-4 text-center hover:border-blue-400 transition-colors bg-gray-50">
                                    <div x-show="!form.qrcodePreview">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" aria-hidden="true" fill="currentColor" class="w-8 h-8 mx-auto mb-3 text-gray-400">
                                            <path fill-rule="evenodd" d="M1.5 6a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 19.5 6v12a2.25 2.25 0 0 1-2.25 2.25H3.75A2.25 2.25 0 0 1 1.5 18V6ZM3 16.06V18c0 .414.336.75.75.75h16.5A.75.75 0 0 0 21 18v-1.94l-2.69-2.689a1.5 1.5 0 0 0-2.12 0l-.88.879.97.97a.75.75 0 1 1-1.06 1.06l-5.16-5.159a1.5 1.5 0 0 0-2.12 0L3 16.061Zm10.125-7.81a1.125 1.125 0 1 1 2.25 0 1.125 1.125 0 0 1-2.25 0Z" clip-rule="evenodd"/>
                                        </svg>
                                        <label for="qrcodeUpload" class="cursor-pointer inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm">
                                            <input id="qrcodeUpload" type="file" accept=".jpg,.jpeg,.png,.webp" class="sr-only" @change="handleQrcodeUpload($event)">
                                            <i class="fas fa-upload mr-2"></i>选择二维码
                                        </label>
                                        <p class="text-xs text-gray-500 mt-2">支持 JPG, PNG, WebP 格式 - 最大 5MB</p>
                                    </div>
                                    <div x-show="form.qrcodePreview" class="relative">
                                        <img :src="form.qrcodePreview" alt="二维码预览" class="h-20 object-contain mx-auto">
                                        <button type="button" @click="removeQrcode()" class="absolute top-0 right-0 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox"
                                   id="allowsParking"
                                   x-model="form.allowsParking"
                                   class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                            <label for="allowsParking" class="ml-2 text-sm font-medium text-gray-700">学校允许停车</label>
                        </div>
                    </div>
                </div>

                <!-- 参展要求卡片 -->
                <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-slate-200 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">参展要求</h2>
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <input type="checkbox"
                                   id="licensePlateRequired"
                                   x-model="form.licensePlateRequired"
                                   class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                            <label for="licensePlateRequired" class="ml-2 text-sm font-medium text-gray-700">参展人员必须填写车牌号码</label>
                        </div>
                        <div>
                            <label for="requirements" class="block text-sm font-medium text-gray-700 mb-2">其他要求</label>
                            <textarea id="requirements"
                                      x-model="form.requirements"
                                      rows="3"
                                      placeholder="请描述其他参展要求..."
                                      class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"></textarea>
                        </div>
                    </div>
                </div>

                <!-- 协办方信息卡片 -->
                <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-slate-200 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">协办方信息</h2>
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <input type="checkbox"
                                   id="hasCoOrganizer"
                                   x-model="form.hasCoOrganizer"
                                   @change="if (form.hasCoOrganizer && !form.selectedCoOrganizer) setDefaultCoOrganizer()"
                                   class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                            <label for="hasCoOrganizer" class="ml-2 text-sm font-medium text-gray-700">是否有协办方</label>
                        </div>

                        <div x-show="form.hasCoOrganizer" x-transition class="space-y-4">
                            <div>
                                <label for="coOrganizerSelect" class="block text-sm font-medium text-gray-700 mb-2">选择协办方</label>
                                <div class="relative">
                                    <input type="text"
                                           id="coOrganizerSelect"
                                           x-model="form.coOrganizerName"
                                           @input="searchCoOrganizers($event.target.value)"
                                           placeholder="搜索协办方组织..."
                                           class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                           autocomplete="off">
                                    <div x-show="showCoOrganizerDropdown"
                                         class="co-organizer-dropdown absolute z-50 w-full bg-white border border-gray-300 rounded-xl shadow-lg mt-1 max-h-60 overflow-y-auto">
                                        <template x-for="organizer in coOrganizers" :key="organizer.id">
                                            <div @click="selectCoOrganizer(organizer)"
                                                 class="px-4 py-3 hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0">
                                                <div class="font-medium text-gray-900" x-text="organizer.name"></div>
                                                <div class="text-sm text-gray-500" x-text="organizer.type === 'publisher' ? '出版社' : '经销商'"></div>
                                            </div>
                                        </template>
                                        <div x-show="coOrganizers.length === 0" class="px-4 py-3 text-gray-500 text-center">
                                            未找到匹配的组织
                                        </div>
                                    </div>
                                </div>
                                <div x-show="form.selectedCoOrganizer" class="mt-2">
                                    <div class="flex items-center justify-between bg-blue-50 border border-blue-200 rounded-xl px-4 py-3">
                                        <span class="text-blue-700 font-medium" x-text="form.selectedCoOrganizer?.name"></span>
                                        <button type="button" @click="removeCoOrganizer()" class="text-red-500 hover:text-red-700">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 联系信息卡片 -->
                <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">联系信息</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="contactName" class="block text-sm font-medium text-gray-700 mb-2 required-field">联系人姓名</label>
                            <input type="text"
                                   id="contactName"
                                   x-model="form.contactName"
                                   placeholder="请输入联系人姓名"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                   required>
                        </div>
                        <div>
                            <label for="contactPhone" class="block text-sm font-medium text-gray-700 mb-2 required-field">联系电话</label>
                            <input type="tel"
                                   id="contactPhone"
                                   x-model="form.contactPhone"
                                   placeholder="请输入联系电话"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                   required>
                        </div>
                    </div>

                    <!-- 联系人保密设置 -->
                    <div class="mt-4">
                        <label class="flex items-center space-x-3 cursor-pointer">
                            <input type="checkbox"
                                   x-model="form.contactIsConfidential"
                                   class="w-4 h-4 text-red-600 border-gray-300 rounded focus:ring-red-500">
                            <span class="text-sm font-medium text-gray-700">
                                <i class="fas fa-eye-slash text-red-500 mr-1"></i>
                                设置联系人信息为保密
                            </span>
                        </label>
                        <p class="text-xs text-gray-500 mt-1 ml-7">
                            勾选后，只有管理员、发起人和协办方可以查看联系人信息
                        </p>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex justify-end space-x-4 pt-6">
                    <button type="button"
                            onclick="history.back()"
                            class="px-6 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors">
                        返回
                    </button>
                    <!-- 取消书展按钮（仅编辑模式且书展未取消/结束时显示） -->
                    <button type="button"
                            x-show="isEditMode && exhibitionData && exhibitionData.status !== 'cancelled' && exhibitionData.status !== 'ended'"
                            @click="showCancelExhibitionModal = true"
                            :disabled="isSubmitting"
                            class="px-6 py-3 bg-red-500 text-white rounded-xl hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                        <i class="fas fa-times-circle mr-2"></i>取消书展
                    </button>
                    <button type="button"
                            @click="saveDraft()"
                            :disabled="isSubmitting"
                            class="px-6 py-3 bg-gray-600 text-white rounded-xl hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                        <span x-show="!isSubmitting">保存草稿</span>
                        <span x-show="isSubmitting" class="flex items-center">
                            <i class="fas fa-spinner fa-spin mr-2"></i>保存中...
                        </span>
                    </button>
                    <button type="submit"
                            :disabled="isSubmitting"
                            class="btn-gradient-primary px-6 py-3 text-white rounded-xl disabled:opacity-50 disabled:cursor-not-allowed">
                        <span x-show="!isSubmitting" x-text="isEditMode ? '更新书展' : '发布书展'"></span>
                        <span x-show="isSubmitting" class="flex items-center">
                            <i class="fas fa-spinner fa-spin mr-2"></i>提交中...
                        </span>
                    </button>
                </div>
            </form>
        </div>

        <!-- 全屏编辑器模态框 -->
        <div x-show="showFullscreenEditor"
             x-cloak
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0">
            <!-- 背景遮罩 -->
            <div style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.8); z-index: 99998;"></div>
            <!-- 模态框内容 -->
            <div style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 99999; width: 90%; max-width: 1200px; height: 80vh; max-height: 700px; min-height: 500px; background: white; border-radius: 1rem; display: flex; flex-direction: column; box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);">
                <div style="padding: 1.5rem 2rem; border-bottom: 1px solid #e5e7eb; display: flex; justify-content: space-between; align-items: center; background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); border-top-left-radius: 1rem; border-top-right-radius: 1rem;">
                    <h3 class="text-lg font-semibold text-gray-900">编辑书展介绍</h3>
                    <div class="flex space-x-2">
                        <button type="button"
                                @click="saveFullscreenContent()"
                                class="btn-gradient-success px-4 py-2 text-white rounded-lg text-sm">
                            <i class="fas fa-check mr-2"></i>保存
                        </button>
                        <button type="button"
                                @click="closeFullscreenEditor()"
                                class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors text-sm">
                            <i class="fas fa-times mr-2"></i>关闭
                        </button>
                    </div>
                </div>
                <div style="flex: 1; display: flex; flex-direction: column; overflow: hidden; border-bottom-left-radius: 1rem; border-bottom-right-radius: 1rem;">
                    <div id="fullscreen-description-editor" style="flex: 1; border-bottom-left-radius: 1rem; border-bottom-right-radius: 1rem;"></div>
                </div>
            </div>
        </div>

        <!-- 取消书展确认模态框 -->
        <div x-show="showCancelExhibitionModal"
             x-cloak
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50">
            <div class="flex items-center justify-center min-h-screen p-4">
                <div class="bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 transform transition-all">
                    <!-- 模态框头部 -->
                    <div class="flex items-center justify-between p-6 border-b border-slate-200">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-times-circle text-red-600"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-bold text-slate-800">取消书展</h3>
                                <p class="text-sm text-slate-500">请确认是否要取消此书展活动</p>
                            </div>
                        </div>
                        <button @click="showCancelExhibitionModal = false" class="w-8 h-8 bg-white hover:bg-slate-50 text-slate-600 rounded-lg flex items-center justify-center transition-colors shadow-sm">
                            <i class="fas fa-times text-sm"></i>
                        </button>
                    </div>

                    <!-- 模态框内容 -->
                    <div class="p-6">
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-slate-700 mb-1">书展名称</label>
                            <p class="font-medium text-slate-900" x-text="form.title || '未设置'"></p>
                        </div>

                        <div class="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                            <div class="flex items-start">
                                <i class="fas fa-exclamation-triangle text-yellow-600 mt-1 mr-3"></i>
                                <div class="text-sm text-yellow-800">
                                    <p class="font-medium mb-1">取消后的影响：</p>
                                    <ul class="list-disc list-inside space-y-1">
                                        <li>书展活动将无法恢复</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="flex gap-3">
                            <button @click="confirmCancelExhibition()"
                                    :disabled="isSubmitting"
                                    class="flex-1 bg-red-500 hover:bg-red-600 text-white px-4 py-3 rounded-xl font-medium transition-colors flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed">
                                <span x-show="!isSubmitting">
                                    <i class="fas fa-times-circle mr-2"></i>确认取消
                                </span>
                                <span x-show="isSubmitting" class="flex items-center">
                                    <i class="fas fa-spinner fa-spin mr-2"></i>取消中...
                                </span>
                            </button>
                            <button @click="showCancelExhibitionModal = false" class="bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-3 rounded-xl font-medium transition-colors">
                                返回
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Alpine.js 数据管理
        function exhibitionManager() {
            return {
                isEditMode: false,
                exhibitionId: null,
                isSubmitting: false,
                showFullscreenEditor: false,
                showCancelExhibitionModal: false,
                descriptionEditor: null,
                fullscreenEditor: null,
                exhibitionData: null,

                form: {
                    title: '',
                    school: '',
                    description: '',
                    startTime: '',
                    endTime: '',
                    registrationDeadline: '',
                    location: '',
                    schoolAddress: '',
                    requiresRegistration: false,
                    registrationRequirements: '',
                    allowsParking: false,
                    licensePlateRequired: false,
                    requirements: '',
                    hasCoOrganizer: false,
                    coOrganizerName: '',
                    selectedCoOrganizer: null,
                    contactName: '',
                    contactPhone: '',
                    contactIsConfidential: false,
                    logoPreview: null,
                    logoFile: null,
                    qrcodePreview: null,
                    qrcodeFile: null
                },

                // 协办方相关数据
                coOrganizers: [],
                showCoOrganizerDropdown: false,

                // 时间提醒
                showTimeWarning: false,

                initialize() {
                    // 检查是否为编辑模式
                    const urlParams = new URLSearchParams(window.location.search);
                    this.exhibitionId = urlParams.get('id');
                    this.isEditMode = !!this.exhibitionId;

                    // 初始化富文本编辑器
                    this.initializeEditor();

                    // 如果是编辑模式，加载数据
                    if (this.isEditMode) {
                        this.loadExhibitionData();
                    } else {
                        // 新建模式，加载用户学校信息
                        this.loadUserSchool();

                        // 处理邀请参数
                        this.handleInvitationParams(urlParams);
                    }

                    // 添加点击事件监听，点击其他地方隐藏协办方下拉框
                    document.addEventListener('click', (e) => {
                        if (!e.target.closest('#coOrganizerSelect') && !e.target.closest('.co-organizer-dropdown')) {
                            this.showCoOrganizerDropdown = false;
                        }
                    });
                },

                initializeEditor() {
                    // 初始化主编辑器
                    this.descriptionEditor = new Quill('#description-editor', {
                        theme: 'snow',
                        placeholder: '请输入书展的详细介绍...',
                        modules: {
                            toolbar: [
                                [{ 'header': [1, 2, 3, false] }],
                                ['bold', 'italic', 'underline', 'strike'],
                                [{ 'color': [] }, { 'background': [] }],
                                [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                                [{ 'align': [] }],
                                ['link', 'image'],
                                ['clean']
                            ]
                        }
                    });

                    // 自定义图片上传处理
                    this.descriptionEditor.getModule('toolbar').addHandler('image', () => {
                        this.selectLocalImage();
                    });
                },

                selectLocalImage() {
                    const input = document.createElement('input');
                    input.setAttribute('type', 'file');
                    input.setAttribute('accept', 'image/*');
                    input.click();

                    input.onchange = () => {
                        const file = input.files[0];
                        if (file) {
                            this.uploadImage(file);
                        }
                    };
                },

                uploadImage(file) {
                    const formData = new FormData();
                    formData.append('file', file);

                    fetch('/api/common/upload/image', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            try {
                                // 确保编辑器有焦点
                                this.descriptionEditor.focus();

                                // 使用三层备选方案插入图片
                                try {
                                    // 方法1: 直接插入到末尾
                                    const length = this.descriptionEditor.getLength();
                                    this.descriptionEditor.insertEmbed(length - 1, 'image', data.data.url);
                                    this.descriptionEditor.insertText(length, '\n'); // 在图片后添加换行
                                    this.showMessage('图片上传成功', 'success');
                                } catch (embedError) {
                                    console.warn('方法1失败，尝试方法2:', embedError);
                                    try {
                                        // 方法2: 使用Delta操作
                                        const Delta = Quill.import('delta');
                                        const delta = new Delta()
                                            .retain(this.descriptionEditor.getLength() - 1)
                                            .insert({ image: data.data.url })
                                            .insert('\n');
                                        this.descriptionEditor.updateContents(delta);
                                        this.showMessage('图片上传成功', 'success');
                                    } catch (deltaError) {
                                        console.warn('方法2失败，尝试方法3:', deltaError);
                                        // 方法3: 直接操作DOM (最后的备选方案)
                                        const editorElement = this.descriptionEditor.root;
                                        const img = document.createElement('img');
                                        img.src = data.data.url;
                                        img.style.width = '33%'; // 设置为原始大小的33%
                                        img.style.height = 'auto';
                                        editorElement.appendChild(img);
                                        editorElement.appendChild(document.createElement('br'));
                                        this.showMessage('图片上传成功', 'success');
                                    }
                                }
                            } catch (error) {
                                console.error('插入图片时发生错误:', error);
                                this.showMessage('图片插入失败，但文件已上传成功', 'warning');
                            }
                        } else {
                            this.showMessage('图片上传失败：' + (data.message || '未知错误'), 'error');
                        }
                    })
                    .catch(error => {
                        console.error('图片上传错误:', error);
                        this.showMessage('图片上传失败，请稍后重试', 'error');
                    });
                },

                openFullscreenEditor() {
                    this.showFullscreenEditor = true;

                    // 延迟初始化全屏编辑器，确保DOM已渲染
                    setTimeout(() => {
                        const editorElement = document.getElementById('fullscreen-description-editor');

                        if (!this.fullscreenEditor && editorElement) {
                            this.fullscreenEditor = new Quill('#fullscreen-description-editor', {
                                theme: 'snow',
                                placeholder: '请输入书展的详细介绍...',
                                modules: {
                                    toolbar: [
                                        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                                        ['bold', 'italic', 'underline', 'strike'],
                                        [{ 'color': [] }, { 'background': [] }],
                                        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                                        [{ 'indent': '-1'}, { 'indent': '+1' }],
                                        [{ 'align': [] }],
                                        ['link', 'image'],
                                        ['clean']
                                    ]
                                }
                            });

                            // 自定义图片上传处理
                            this.fullscreenEditor.getModule('toolbar').addHandler('image', () => {
                                this.selectLocalImageForFullscreen();
                            });
                        }

                        // 同步内容
                        if (this.fullscreenEditor && this.descriptionEditor) {
                            this.fullscreenEditor.setContents(this.descriptionEditor.getContents());
                        }
                    }, 200);
                },

                selectLocalImageForFullscreen() {
                    const input = document.createElement('input');
                    input.setAttribute('type', 'file');
                    input.setAttribute('accept', 'image/*');
                    input.click();

                    input.onchange = () => {
                        const file = input.files[0];
                        if (file) {
                            this.uploadImageForFullscreen(file);
                        }
                    };
                },

                uploadImageForFullscreen(file) {
                    const formData = new FormData();
                    formData.append('file', file);

                    fetch('/api/common/upload/image', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            try {
                                // 确保编辑器有焦点
                                this.fullscreenEditor.focus();

                                // 使用三层备选方案插入图片
                                try {
                                    // 方法1: 直接插入到末尾
                                    const length = this.fullscreenEditor.getLength();
                                    this.fullscreenEditor.insertEmbed(length - 1, 'image', data.data.url);
                                    this.fullscreenEditor.insertText(length, '\n'); // 在图片后添加换行
                                    this.showMessage('图片上传成功', 'success');
                                } catch (embedError) {
                                    console.warn('方法1失败，尝试方法2:', embedError);
                                    try {
                                        // 方法2: 使用Delta操作
                                        const Delta = Quill.import('delta');
                                        const delta = new Delta()
                                            .retain(this.fullscreenEditor.getLength() - 1)
                                            .insert({ image: data.data.url })
                                            .insert('\n');
                                        this.fullscreenEditor.updateContents(delta);
                                        this.showMessage('图片上传成功', 'success');
                                    } catch (deltaError) {
                                        console.warn('方法2失败，尝试方法3:', deltaError);
                                        // 方法3: 直接操作DOM (最后的备选方案)
                                        const editorElement = this.fullscreenEditor.root;
                                        const img = document.createElement('img');
                                        img.src = data.data.url;
                                        img.style.width = '33%'; // 设置为原始大小的33%
                                        img.style.height = 'auto';
                                        editorElement.appendChild(img);
                                        editorElement.appendChild(document.createElement('br'));
                                        this.showMessage('图片上传成功', 'success');
                                    }
                                }
                            } catch (error) {
                                console.error('插入图片时发生错误:', error);
                                this.showMessage('图片插入失败，但文件已上传成功', 'warning');
                            }
                        } else {
                            this.showMessage('图片上传失败：' + (data.message || '未知错误'), 'error');
                        }
                    })
                    .catch(error => {
                        console.error('图片上传错误:', error);
                        this.showMessage('图片上传失败，请稍后重试', 'error');
                    });
                },

                saveFullscreenContent() {
                    try {
                        // 将全屏编辑器的内容同步到主编辑器
                        if (this.fullscreenEditor && this.descriptionEditor) {
                            this.descriptionEditor.setContents(this.fullscreenEditor.getContents());
                            this.showMessage('内容已保存', 'success');
                        }
                        this.closeFullscreenEditor();
                    } catch (error) {
                        console.error('保存全屏内容失败:', error);
                        this.closeFullscreenEditor();
                    }
                },

                closeFullscreenEditor() {
                    this.showFullscreenEditor = false;
                    // 确保模态框完全关闭
                    this.$nextTick(() => {
                        document.body.style.overflow = '';
                    });
                },

                handleLogoUpload(event) {
                    const file = event.target.files[0];
                    if (file) {
                        if (file.size > 5 * 1024 * 1024) {
                            this.showMessage('文件大小不能超过5MB', 'error');
                            return;
                        }

                        this.form.logoFile = file;
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            this.form.logoPreview = e.target.result;
                        };
                        reader.readAsDataURL(file);
                    }
                },

                removeLogo() {
                    this.form.logoPreview = null;
                    this.form.logoFile = null;
                    document.getElementById('logoUpload').value = '';
                },

                handleQrcodeUpload(event) {
                    const file = event.target.files[0];
                    if (file) {
                        if (file.size > 5 * 1024 * 1024) {
                            this.showMessage('文件大小不能超过5MB', 'error');
                            return;
                        }

                        this.form.qrcodeFile = file;
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            this.form.qrcodePreview = e.target.result;
                        };
                        reader.readAsDataURL(file);
                    }
                },

                removeQrcode() {
                    this.form.qrcodePreview = null;
                    this.form.qrcodeFile = null;
                    document.getElementById('qrcodeUpload').value = '';
                },

                loadUserSchool() {
                    fetch('/api/common/get_user_info')
                        .then(response => response.json())
                        .then(data => {
                            if (data.status === 'success') {
                                this.form.school = data.user_info.school_name || '';
                                this.form.contactName = data.user_info.name || '';
                                this.form.contactPhone = data.user_info.phone_number || '';
                            }
                        })
                        .catch(error => {
                            console.error('加载用户信息失败:', error);
                        });
                },

                loadExhibitionData() {
                    fetch(`/api/teacher/get_exhibition_detail?id=${this.exhibitionId}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.code === 0) {
                                const exhibition = data.data;
                                this.exhibitionData = exhibition; // 保存书展数据用于取消按钮显示判断
                                console.log('加载的书展数据:', exhibition); // 调试信息
                                this.form.title = exhibition.title || '';
                                this.form.school = exhibition.school_name || '';
                                this.form.startTime = exhibition.start_time || '';
                                this.form.endTime = exhibition.end_time || '';
                                this.form.registrationDeadline = exhibition.registration_deadline || '';
                                this.form.location = exhibition.location || '';
                                this.form.schoolAddress = exhibition.school_address || '';
                                // 布尔值字段需要强制转换，因为数据库可能返回 0/1 或字符串
                                this.form.requiresRegistration = !!(exhibition.requires_campus_registration === true || exhibition.requires_campus_registration === 1 || exhibition.requires_campus_registration === '1');
                                this.form.registrationRequirements = exhibition.registration_requirements || '';
                                this.form.allowsParking = !!(exhibition.allows_parking === true || exhibition.allows_parking === 1 || exhibition.allows_parking === '1');
                                this.form.licensePlateRequired = !!(exhibition.license_plate_required === true || exhibition.license_plate_required === 1 || exhibition.license_plate_required === '1');
                                this.form.requirements = exhibition.requirements || '';
                                this.form.hasCoOrganizer = !!(exhibition.co_organizer_type && exhibition.co_organizer_id);
                                if (this.form.hasCoOrganizer) {
                                    this.form.selectedCoOrganizer = {
                                        id: exhibition.co_organizer_id,
                                        name: exhibition.co_organizer_name,
                                        type: exhibition.co_organizer_type
                                    };
                                    this.form.coOrganizerName = exhibition.co_organizer_name || '';
                                }
                                // 联系人信息可能在 initiator 对象中
                                if (exhibition.initiator) {
                                    this.form.contactName = exhibition.initiator.name || '';
                                    this.form.contactPhone = exhibition.initiator.phone || '';
                                } else {
                                    this.form.contactName = exhibition.initiator_name || '';
                                    this.form.contactPhone = exhibition.initiator_phone || '';
                                }

                                // 加载联系人保密设置
                                this.form.contactIsConfidential = exhibition.contact_is_confidential || false;

                                // 设置富文本内容
                                if (exhibition.description) {
                                    this.descriptionEditor.root.innerHTML = exhibition.description;
                                }

                                // 设置图片预览
                                if (exhibition.logo_url) {
                                    this.form.logoPreview = exhibition.logo_url;
                                }
                                if (exhibition.registration_qrcode) {
                                    this.form.qrcodePreview = exhibition.registration_qrcode;
                                }
                            } else {
                                this.showMessage('加载书展信息失败：' + (data.message || '未知错误'), 'error');
                            }
                        })
                        .catch(error => {
                            console.error('加载书展信息失败:', error);
                            this.showMessage('加载书展信息失败，请稍后重试', 'error');
                        });
                },

                validateForm() {
                    if (!this.form.title.trim()) {
                        this.showMessage('请输入书展主题', 'error');
                        return false;
                    }

                    if (!this.form.startTime) {
                        this.showMessage('请选择开始时间', 'error');
                        return false;
                    }

                    if (!this.form.endTime) {
                        this.showMessage('请选择结束时间', 'error');
                        return false;
                    }

                    if (!this.form.registrationDeadline) {
                        this.showMessage('请选择报名截止时间', 'error');
                        return false;
                    }

                    if (!this.form.location.trim()) {
                        this.showMessage('请输入具体地点', 'error');
                        return false;
                    }

                    if (!this.form.schoolAddress.trim()) {
                        this.showMessage('请输入学校地址', 'error');
                        return false;
                    }

                    if (!this.form.contactName.trim()) {
                        this.showMessage('请输入联系人姓名', 'error');
                        return false;
                    }

                    if (!this.form.contactPhone.trim()) {
                        this.showMessage('请输入联系电话', 'error');
                        return false;
                    }

                    // 验证时间逻辑
                    const startTime = new Date(this.form.startTime);
                    const endTime = new Date(this.form.endTime);
                    const deadline = new Date(this.form.registrationDeadline);

                    if (startTime >= endTime) {
                        this.showMessage('结束时间必须晚于开始时间', 'error');
                        return false;
                    }

                    if (deadline >= startTime) {
                        this.showMessage('报名截止时间必须早于开始时间', 'error');
                        return false;
                    }

                    return true;
                },

                async submitForm() {
                    if (!this.validateForm()) {
                        return;
                    }

                    this.isSubmitting = true;

                    try {
                        // 准备表单数据
                        const formData = new FormData();

                        // 基本信息 - 使用后端期望的字段名
                        formData.append('title', this.form.title);
                        formData.append('description', this.descriptionEditor.root.innerHTML);
                        formData.append('startTime', this.form.startTime);
                        formData.append('endTime', this.form.endTime);
                        formData.append('registrationDeadline', this.form.registrationDeadline);
                        formData.append('location', this.form.location);
                        formData.append('schoolAddress', this.form.schoolAddress);
                        formData.append('requiresCampusRegistration', this.form.requiresRegistration ? 'on' : '');
                        formData.append('registrationRequirements', this.form.registrationRequirements);
                        formData.append('allowsParking', this.form.allowsParking ? 'on' : '');
                        formData.append('licensePlateRequired', this.form.licensePlateRequired ? 'on' : '');
                        formData.append('requirements', this.form.requirements);
                        formData.append('hasCoOrganizer', this.form.hasCoOrganizer ? 'on' : '');
                        if (this.form.hasCoOrganizer && this.form.selectedCoOrganizer) {
                            formData.append('coOrganizerType', this.form.selectedCoOrganizer.type);
                            formData.append('coOrganizerId', this.form.selectedCoOrganizer.id);
                        }
                        formData.append('initiatorName', this.form.contactName);
                        formData.append('initiatorPhone', this.form.contactPhone);
                        formData.append('contactIsConfidential', this.form.contactIsConfidential);
                        formData.append('status', 'published');

                        // 如果是编辑模式，添加exhibition ID
                        if (this.isEditMode) {
                            formData.append('exhibitionId', this.exhibitionId);
                        }

                        // 文件上传 - 使用后端期望的字段名
                        if (this.form.logoFile) {
                            formData.append('logoUpload', this.form.logoFile);
                        }
                        if (this.form.qrcodeFile) {
                            formData.append('registrationQrcodeUpload', this.form.qrcodeFile);
                        }

                        // 发送请求到现有的API
                        const response = await fetch('/api/teacher/save_exhibition', {
                            method: 'POST',
                            body: formData
                        });

                        const data = await response.json();

                        if (data.code === 0) {
                            this.showMessage(
                                this.isEditMode ? '书展更新成功！' : '书展发布成功！',
                                'success'
                            );

                            // 延迟跳转
                            setTimeout(() => {
                                window.location.href = '/teacher/exhibitions';
                            }, 1500);
                        } else {
                            this.showMessage('操作失败：' + (data.message || '未知错误'), 'error');
                        }
                    } catch (error) {
                        console.error('提交失败:', error);
                        this.showMessage('网络错误，请稍后重试', 'error');
                    } finally {
                        this.isSubmitting = false;
                    }
                },

                async confirmCancelExhibition() {
                    if (!this.exhibitionId) {
                        this.showMessage('书展ID不存在', 'error');
                        return;
                    }

                    this.isSubmitting = true;

                    try {
                        const formData = new FormData();
                        formData.append('id', this.exhibitionId);
                        formData.append('status', 'cancelled');

                        const response = await fetch('/api/teacher/change_exhibition_status', {
                            method: 'POST',
                            body: formData
                        });

                        const data = await response.json();

                        if (data.code === 0) {
                            this.showMessage(data.message || '书展已成功取消', 'success');
                            this.showCancelExhibitionModal = false;

                            // 延迟跳转到书展管理页面
                            setTimeout(() => {
                                window.location.href = '/teacher/exhibitions';
                            }, 1500);
                        } else {
                            this.showMessage(data.message || '取消书展失败', 'error');
                        }
                    } catch (error) {
                        console.error('取消书展失败:', error);
                        this.showMessage('网络错误，请稍后重试', 'error');
                    } finally {
                        this.isSubmitting = false;
                    }
                },

                async saveDraft() {
                    if (!this.form.title.trim()) {
                        this.showMessage('请至少输入书展主题', 'error');
                        return;
                    }

                    this.isSubmitting = true;

                    try {
                        // 准备表单数据
                        const formData = new FormData();

                        // 基本信息 - 使用后端期望的字段名，为草稿提供默认值
                        formData.append('title', this.form.title);
                        formData.append('description', this.descriptionEditor.root.innerHTML);
                        formData.append('startTime', this.form.startTime || '2024-01-01 09:00');
                        formData.append('endTime', this.form.endTime || '2024-01-01 17:00');
                        formData.append('registrationDeadline', this.form.registrationDeadline || '2024-01-01 09:00');
                        formData.append('location', this.form.location || '待定');
                        formData.append('schoolAddress', this.form.schoolAddress || '待定');
                        formData.append('requiresCampusRegistration', this.form.requiresRegistration ? 'on' : '');
                        formData.append('registrationRequirements', this.form.registrationRequirements || '');
                        formData.append('allowsParking', this.form.allowsParking ? 'on' : '');
                        formData.append('licensePlateRequired', this.form.licensePlateRequired ? 'on' : '');
                        formData.append('requirements', this.form.requirements || '');
                        formData.append('hasCoOrganizer', this.form.hasCoOrganizer ? 'on' : '');
                        if (this.form.hasCoOrganizer && this.form.selectedCoOrganizer) {
                            formData.append('coOrganizerType', this.form.selectedCoOrganizer.type);
                            formData.append('coOrganizerId', this.form.selectedCoOrganizer.id);
                        }
                        formData.append('initiatorName', this.form.contactName || '待定');
                        formData.append('initiatorPhone', this.form.contactPhone || '00000000000');
                        formData.append('contactIsConfidential', this.form.contactIsConfidential);
                        formData.append('status', 'draft');

                        // 如果是编辑模式，添加exhibition ID
                        if (this.isEditMode) {
                            formData.append('exhibitionId', this.exhibitionId);
                        }

                        // 文件上传 - 使用后端期望的字段名
                        if (this.form.logoFile) {
                            formData.append('logoUpload', this.form.logoFile);
                        }
                        if (this.form.qrcodeFile) {
                            formData.append('registrationQrcodeUpload', this.form.qrcodeFile);
                        }

                        // 发送请求到现有的API
                        const response = await fetch('/api/teacher/save_exhibition', {
                            method: 'POST',
                            body: formData
                        });

                        const data = await response.json();

                        if (data.code === 0) {
                            this.showMessage('草稿保存成功！', 'success');

                            // 如果是新建，更新为编辑模式
                            if (!this.isEditMode && data.exhibition_id) {
                                this.isEditMode = true;
                                this.exhibitionId = data.exhibition_id;
                                // 更新URL
                                const newUrl = new URL(window.location);
                                newUrl.searchParams.set('id', this.exhibitionId);
                                window.history.replaceState({}, '', newUrl);
                            }
                        } else {
                            this.showMessage('保存失败：' + (data.message || '未知错误'), 'error');
                        }
                    } catch (error) {
                        console.error('保存失败:', error);
                        this.showMessage('网络错误，请稍后重试', 'error');
                    } finally {
                        this.isSubmitting = false;
                    }
                },

                // 搜索协办方
                searchCoOrganizers(query) {
                    if (query.trim().length < 1) {
                        this.showCoOrganizerDropdown = false;
                        return;
                    }

                    fetch(`/api/teacher/search_co_organizers?query=${encodeURIComponent(query)}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.code === 0) {
                                this.coOrganizers = data.data || [];
                                this.showCoOrganizerDropdown = true;
                            } else {
                                this.coOrganizers = [];
                                this.showCoOrganizerDropdown = true;
                            }
                        })
                        .catch(error => {
                            console.error('搜索协办方失败:', error);
                            this.coOrganizers = [];
                            this.showCoOrganizerDropdown = false;
                        });
                },

                // 设置默认协办方
                setDefaultCoOrganizer() {
                    // 搜索广东新华发行集团股份有限公司
                    fetch('/api/teacher/search_co_organizers?query=广东新华发行集团股份有限公司')
                        .then(response => response.json())
                        .then(data => {
                            if (data.code === 0 && data.data && data.data.length > 0) {
                                // 查找匹配的组织
                                const defaultOrg = data.data.find(org =>
                                    org.name.includes('广东新华发行集团') && org.type === 'dealer'
                                );
                                if (defaultOrg) {
                                    this.selectCoOrganizer(defaultOrg);
                                }
                            }
                        })
                        .catch(error => {
                            console.error('设置默认协办方失败:', error);
                        });
                },

                // 选择协办方
                selectCoOrganizer(organizer) {
                    this.form.selectedCoOrganizer = organizer;
                    this.form.coOrganizerName = organizer.name;
                    this.showCoOrganizerDropdown = false;
                },

                // 检查时间提前量
                checkTimeAdvance() {
                    if (!this.form.startTime) {
                        this.showTimeWarning = false;
                        return;
                    }

                    const startDate = new Date(this.form.startTime);
                    const currentDate = new Date();
                    const diffTime = startDate.getTime() - currentDate.getTime();
                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                    // 如果开始时间距离当前时间小于15天，显示提醒
                    this.showTimeWarning = diffDays < 15;
                },

                // 处理邀请参数
                handleInvitationParams(urlParams) {
                    const inviterType = urlParams.get('inviter_type');
                    const inviterId = urlParams.get('inviter_id');
                    const inviterName = urlParams.get('inviter_name');

                    if (inviterType && inviterId && inviterName) {
                        // 自动勾选有协办方
                        this.form.hasCoOrganizer = true;

                        // 设置协办方信息
                        this.form.selectedCoOrganizer = {
                            id: parseInt(inviterId),
                            name: decodeURIComponent(inviterName),
                            type: inviterType
                        };
                        this.form.coOrganizerName = decodeURIComponent(inviterName);

                        // 显示友好的邀请提示
                        setTimeout(() => {
                            this.showInvitationWelcome(decodeURIComponent(inviterName), inviterType);
                        }, 500);
                    }
                },

                // 显示邀请欢迎信息
                showInvitationWelcome(inviterName, inviterType) {
                    const welcomeDiv = document.createElement('div');
                    welcomeDiv.className = 'invitation-welcome-modal';
                    welcomeDiv.innerHTML = `
                        <div class="invitation-welcome-overlay" onclick="this.parentElement.remove()"></div>
                        <div class="invitation-welcome-content">
                            <div class="invitation-welcome-header">
                                <div class="invitation-welcome-icon">
                                    <i class="fas fa-paper-plane"></i>
                                </div>
                                <h3>受邀创建书展</h3>
                                <button onclick="this.closest('.invitation-welcome-modal').remove()" class="invitation-welcome-close">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <div class="invitation-welcome-body">
                                <p class="invitation-welcome-message">
                                    <strong>${inviterName}</strong> 邀请您创建书展
                                </p>
                                <p class="invitation-welcome-tip">
                                    协办方信息已自动填充，您可以直接开始创建书展
                                </p>
                            </div>
                            <div class="invitation-welcome-footer">
                                <button onclick="this.closest('.invitation-welcome-modal').remove()" class="btn-primary">
                                    我知道了
                                </button>
                            </div>
                        </div>
                    `;

                    // 添加CSS样式
                    if (!document.getElementById('invitation-welcome-styles')) {
                        const style = document.createElement('style');
                        style.id = 'invitation-welcome-styles';
                        style.textContent = `
                            .invitation-welcome-modal {
                                position: fixed;
                                top: 0;
                                left: 0;
                                width: 100%;
                                height: 100%;
                                z-index: 9999;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                padding: 16px;
                                animation: fadeIn 0.3s ease-out;
                            }

                            .invitation-welcome-overlay {
                                position: absolute;
                                top: 0;
                                left: 0;
                                width: 100%;
                                height: 100%;
                                background: rgba(0, 0, 0, 0.5);
                                backdrop-filter: blur(4px);
                            }

                            .invitation-welcome-content {
                                position: relative;
                                background: white;
                                border-radius: 12px;
                                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
                                max-width: 400px;
                                width: 100%;
                                overflow: hidden;
                                animation: slideInScale 0.3s ease-out;
                            }

                            .invitation-welcome-header {
                                display: flex;
                                align-items: center;
                                padding: 20px 24px 16px;
                                border-bottom: 1px solid #e2e8f0;
                                position: relative;
                            }

                            .invitation-welcome-icon {
                                width: 40px;
                                height: 40px;
                                background: #dbeafe;
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                color: #2563eb;
                                margin-right: 12px;
                                flex-shrink: 0;
                            }

                            .invitation-welcome-header h3 {
                                font-size: 18px;
                                font-weight: 600;
                                color: #1e293b;
                                margin: 0;
                                flex: 1;
                            }

                            .invitation-welcome-close {
                                width: 32px;
                                height: 32px;
                                background: #f1f5f9;
                                border: none;
                                border-radius: 6px;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                color: #64748b;
                                cursor: pointer;
                                transition: all 0.2s ease;
                                flex-shrink: 0;
                            }

                            .invitation-welcome-close:hover {
                                background: #e2e8f0;
                            }

                            .invitation-welcome-body {
                                padding: 20px 24px;
                            }

                            .invitation-welcome-message {
                                font-size: 16px;
                                color: #374151;
                                margin: 0 0 12px 0;
                                line-height: 1.5;
                            }

                            .invitation-welcome-tip {
                                font-size: 14px;
                                color: #6b7280;
                                margin: 0;
                                line-height: 1.4;
                            }

                            .invitation-welcome-footer {
                                padding: 16px 24px 20px;
                                text-align: center;
                            }

                            .invitation-welcome-footer .btn-primary {
                                padding: 10px 24px;
                                font-size: 14px;
                            }

                            @keyframes fadeIn {
                                from { opacity: 0; }
                                to { opacity: 1; }
                            }

                            @keyframes slideInScale {
                                from {
                                    opacity: 0;
                                    transform: scale(0.9) translateY(-10px);
                                }
                                to {
                                    opacity: 1;
                                    transform: scale(1) translateY(0);
                                }
                            }

                            /* 移动端适配 */
                            @media (max-width: 640px) {
                                .invitation-welcome-modal {
                                    padding: 12px;
                                }

                                .invitation-welcome-content {
                                    max-width: none;
                                    margin: 0;
                                }

                                .invitation-welcome-header {
                                    padding: 16px 20px 12px;
                                }

                                .invitation-welcome-header h3 {
                                    font-size: 16px;
                                }

                                .invitation-welcome-icon {
                                    width: 36px;
                                    height: 36px;
                                    margin-right: 10px;
                                }

                                .invitation-welcome-body {
                                    padding: 16px 20px;
                                }

                                .invitation-welcome-message {
                                    font-size: 15px;
                                }

                                .invitation-welcome-tip {
                                    font-size: 13px;
                                }

                                .invitation-welcome-footer {
                                    padding: 12px 20px 16px;
                                }
                            }

                            /* 小屏幕适配 */
                            @media (max-width: 480px) {
                                .invitation-welcome-modal {
                                    padding: 8px;
                                }

                                .invitation-welcome-header {
                                    padding: 14px 16px 10px;
                                }

                                .invitation-welcome-body {
                                    padding: 14px 16px;
                                }

                                .invitation-welcome-footer {
                                    padding: 10px 16px 14px;
                                }
                            }
                        `;
                        document.head.appendChild(style);
                    }

                    document.body.appendChild(welcomeDiv);

                    // 3秒后自动关闭
                    setTimeout(() => {
                        if (document.body.contains(welcomeDiv)) {
                            welcomeDiv.style.opacity = '0';
                            setTimeout(() => {
                                if (document.body.contains(welcomeDiv)) {
                                    welcomeDiv.remove();
                                }
                            }, 300);
                        }
                    }, 3000);
                },

                // 移除协办方
                removeCoOrganizer() {
                    this.form.selectedCoOrganizer = null;
                    this.form.coOrganizerName = '';
                    this.showCoOrganizerDropdown = false;
                },

                showMessage(message, type = 'success') {
                    const id = Date.now();
                    const typeClass = type === 'success' ? 'bg-green-100 text-green-800 border-green-200' : 'bg-red-100 text-red-800 border-red-200';
                    const icon = type === 'success' ? 'check-circle' : 'exclamation-circle';

                    const messageHtml = `
                        <div id="message-${id}" class="message-toast ${typeClass} px-4 py-3 rounded-lg shadow-md border mb-3 animate-fadeIn">
                            <div class="flex items-center">
                                <i class="fas fa-${icon} mr-2"></i>
                                <span>${message}</span>
                            </div>
                        </div>
                    `;

                    document.getElementById('messageContainer').insertAdjacentHTML('beforeend', messageHtml);

                    // 3秒后自动移除
                    setTimeout(() => {
                        const messageEl = document.getElementById(`message-${id}`);
                        if (messageEl) {
                            messageEl.classList.remove('animate-fadeIn');
                            messageEl.classList.add('animate-fadeOut');
                            setTimeout(() => {
                                messageEl.remove();
                            }, 300);
                        }
                    }, 3000);
                }
            };
        }
    </script>
</body>
</html>
